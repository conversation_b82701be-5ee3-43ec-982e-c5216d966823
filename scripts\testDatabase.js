require('dotenv').config();
const database = require('../config/database');
const DataStore = require('../utils/dataStore');

async function testDatabaseOperations() {
  try {
    console.log('Starting database tests...');
    
    // Connect to database
    await database.connect();
    console.log('✓ Database connected successfully');

    // Test 1: Get users
    console.log('\n--- Testing User Operations ---');
    const users = await DataStore.getUsers();
    console.log(`✓ Found ${users.length} users`);

    if (users.length > 0) {
      const firstUser = users[0];
      console.log(`✓ First user: ${firstUser.username} (${firstUser._id})`);
      
      // Test getting user by ID
      const userById = await DataStore.getUserById(firstUser._id);
      console.log(`✓ Get user by ID: ${userById ? userById.username : 'Not found'}`);
      
      // Test getting user by username
      const userByUsername = await DataStore.getUserByUsername(firstUser.username);
      console.log(`✓ Get user by username: ${userByUsername ? userByUsername.username : 'Not found'}`);
    }

    // Test 2: Get messages
    console.log('\n--- Testing Message Operations ---');
    const messages = await DataStore.readData('messages.json');
    console.log(`✓ Found ${messages.length} messages`);

    if (users.length > 0) {
      const userId = users[0]._id;
      const userMessages = await DataStore.getMessages(userId);
      console.log(`✓ Found ${userMessages.length} messages for user ${userId}`);
      
      const recentMessages = await DataStore.getRecentMessages(userId, 3);
      console.log(`✓ Found ${recentMessages.length} recent messages`);
      
      const conversationHistory = await DataStore.getConversationHistory(userId, 3);
      console.log(`✓ Found ${conversationHistory.length} conversation history messages`);
    }

    // Test 3: Get products
    console.log('\n--- Testing Product Operations ---');
    if (users.length > 0) {
      const userId = users[0]._id;
      const products = await DataStore.getProducts(userId);
      console.log(`✓ Found ${products.length} products for user ${userId}`);
      
      if (products.length > 0) {
        console.log(`✓ First product: ${products[0].name} - ${products[0].price}`);
      }
    }

    // Test 4: Get store info
    console.log('\n--- Testing Store Info Operations ---');
    if (users.length > 0) {
      const userId = users[0]._id;
      const storeInfo = await DataStore.getStoreInfo(userId);
      console.log(`✓ Store info for user ${userId}: ${storeInfo.name || 'No name set'}`);
    }

    // Test 5: Get orders
    console.log('\n--- Testing Order Operations ---');
    if (users.length > 0) {
      const userId = users[0]._id;
      const orders = await DataStore.getOrders(userId);
      console.log(`✓ Found ${orders.length} orders for user ${userId}`);
    }

    // Test 6: Get activation codes
    console.log('\n--- Testing Activation Code Operations ---');
    const activationCodes = await DataStore.getActivationCodes();
    console.log(`✓ Found ${activationCodes.length} activation codes`);

    // Test 7: Get tokens
    console.log('\n--- Testing Token Operations ---');
    const tokens = await DataStore.readData('tokens.json');
    console.log(`✓ Found ${tokens.length} tokens`);

    console.log('\n✅ All database tests completed successfully!');
    
  } catch (error) {
    console.error('❌ Database test failed:', error);
  } finally {
    // Disconnect from database
    await database.disconnect();
    console.log('✓ Disconnected from database');
    process.exit(0);
  }
}

// Run the tests
testDatabaseOperations();
