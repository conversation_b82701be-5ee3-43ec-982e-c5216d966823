require('dotenv').config();
const axios = require('axios');
const database = require('../config/database');

const BASE_URL = 'http://localhost:3000';
let authToken = null;
let testUserId = null;

// Test data
const testUser = {
  username: 'chatordertest_' + Date.now(),
  password: 'testpassword123',
  email: '<EMAIL>',
  name: 'Chat Order Test User'
};

async function makeRequest(method, endpoint, data = null, useAuth = false) {
  try {
    const config = {
      method,
      url: `${BASE_URL}${endpoint}`,
      headers: {}
    };

    if (useAuth && authToken) {
      config.headers['x-auth-token'] = authToken;
    }

    if (data) {
      config.data = data;
      config.headers['Content-Type'] = 'application/json';
    }

    const response = await axios(config);
    return { success: true, data: response.data, status: response.status };
  } catch (error) {
    return {
      success: false,
      error: error.response ? error.response.data : error.message,
      status: error.response ? error.response.status : 500
    };
  }
}

async function setupTestUser() {
  console.log('\n--- إعداد مستخدم الاختبار ---');
  
  const result = await makeRequest('POST', '/api/auth/register', testUser);
  
  if (result.success) {
    console.log('✅ تم إنشاء مستخدم الاختبار بنجاح');
    authToken = result.data.token;
    testUserId = result.data.user.id;
    return true;
  } else {
    console.log('❌ فشل في إنشاء مستخدم الاختبار:', result.error);
    return false;
  }
}

async function setupTestProducts() {
  console.log('\n--- إعداد منتجات الاختبار ---');
  
  const products = [
    {
      name: 'آيفون 15 برو',
      price: '1200 دولار',
      description: 'هاتف ذكي متطور من شركة آبل'
    },
    {
      name: 'آيربودز برو',
      price: '250 دولار',
      description: 'سماعات لاسلكية بخاصية إلغاء الضوضاء'
    },
    {
      name: 'آبل ووتش',
      price: '400 دولار',
      description: 'ساعة ذكية بمزايا صحية متقدمة'
    }
  ];
  
  const addedProducts = [];
  
  for (const product of products) {
    const result = await makeRequest('POST', '/api/products', product, true);
    if (result.success) {
      addedProducts.push(result.data);
      console.log(`✅ تم إضافة المنتج: ${product.name}`);
    } else {
      console.log(`❌ فشل في إضافة المنتج: ${product.name}`);
    }
  }
  
  return addedProducts;
}

async function setupStoreInfo() {
  console.log('\n--- إعداد معلومات المتجر ---');
  
  const storeInfo = {
    name: 'متجر التكنولوجيا المتقدمة',
    address: 'شارع الحبيبية، بغداد، العراق',
    description: 'متجر متخصص في بيع أحدث المنتجات التكنولوجية والإلكترونية',
    phone: '+964 ************',
    email: '<EMAIL>'
  };
  
  const result = await makeRequest('POST', '/api/store/info', storeInfo, true);
  
  if (result.success) {
    console.log('✅ تم إعداد معلومات المتجر بنجاح');
    return result.data;
  } else {
    console.log('❌ فشل في إعداد معلومات المتجر:', result.error);
    return null;
  }
}

async function testChatOrderCreation() {
  console.log('\n--- اختبار إنشاء الطلبات من المحادثة ---');
  
  // Test scenario 1: Request product without providing customer info
  console.log('\n🔸 السيناريو 1: طلب منتج بدون تقديم معلومات العميل');
  
  let result = await makeRequest('POST', '/api/messages/chat', {
    message: 'أريد شراء آيفون 15 برو',
    platform: 'test'
  }, true);
  
  if (result.success) {
    console.log('✅ تم إرسال طلب المنتج');
    console.log(`   الرد: ${result.data.message.substring(0, 100)}...`);
    console.log(`   حالة الطلب: ${result.data.orderStatus || 'لا يوجد'}`);
    console.log(`   تم إنشاء طلب: ${result.data.orderCreated ? 'نعم' : 'لا'}`);
  } else {
    console.log('❌ فشل في إرسال طلب المنتج:', result.error);
  }
  
  // Test scenario 2: Provide customer information
  console.log('\n🔸 السيناريو 2: تقديم معلومات العميل');
  
  result = await makeRequest('POST', '/api/messages/chat', {
    message: 'اسمي أحمد محمد، رقمي 07901234567، وعنواني بغداد الكرادة',
    platform: 'test'
  }, true);
  
  if (result.success) {
    console.log('✅ تم إرسال معلومات العميل');
    console.log(`   الرد: ${result.data.message.substring(0, 100)}...`);
    console.log(`   حالة الطلب: ${result.data.orderStatus || 'لا يوجد'}`);
    console.log(`   تم إنشاء طلب: ${result.data.orderCreated ? 'نعم' : 'لا'}`);
  } else {
    console.log('❌ فشل في إرسال معلومات العميل:', result.error);
  }
  
  // Test scenario 3: Complete order in one message
  console.log('\n🔸 السيناريو 3: طلب كامل في رسالة واحدة');
  
  result = await makeRequest('POST', '/api/messages/chat', {
    message: 'أريد شراء آيربودز برو، اسمي فاطمة علي، رقمي 07801234567، عنواني البصرة',
    platform: 'test'
  }, true);
  
  if (result.success) {
    console.log('✅ تم إرسال طلب كامل');
    console.log(`   الرد: ${result.data.message.substring(0, 100)}...`);
    console.log(`   حالة الطلب: ${result.data.orderStatus || 'لا يوجد'}`);
    console.log(`   تم إنشاء طلب: ${result.data.orderCreated ? 'نعم' : 'لا'}`);
  } else {
    console.log('❌ فشل في إرسال طلب كامل:', result.error);
  }
}

async function testOrdersAPI() {
  console.log('\n--- اختبار API الطلبات ---');
  
  // Get all orders
  const result = await makeRequest('GET', '/api/orders', null, true);
  
  if (result.success) {
    console.log(`✅ تم استرجاع ${result.data.length} طلب`);
    
    result.data.forEach((order, index) => {
      console.log(`\n   الطلب ${index + 1}:`);
      console.log(`     ID: ${order._id}`);
      console.log(`     المنتج: ${order.productName || 'غير محدد'}`);
      console.log(`     الكمية: ${order.quantity || 'غير محدد'}`);
      console.log(`     اسم العميل: ${order.customerName || 'غير محدد'}`);
      console.log(`     هاتف العميل: ${order.customerPhone || 'غير محدد'}`);
      console.log(`     عنوان العميل: ${order.customerAddress || 'غير محدد'}`);
      console.log(`     المبلغ الإجمالي: ${order.totalAmount || 'غير محدد'}`);
      console.log(`     الحالة: ${order.status || 'غير محدد'}`);
      console.log(`     المصدر: ${order.source || 'غير محدد'}`);
      console.log(`     التاريخ: ${new Date(order.createdAt).toLocaleString('ar-EG')}`);
    });
    
    return result.data;
  } else {
    console.log('❌ فشل في استرجاع الطلبات:', result.error);
    return [];
  }
}

async function testOrderManagement(orders) {
  console.log('\n--- اختبار إدارة الطلبات ---');
  
  if (orders.length === 0) {
    console.log('لا توجد طلبات للاختبار');
    return;
  }
  
  const order = orders[0];
  
  // Test updating order status
  console.log('\n🔸 اختبار تحديث حالة الطلب');
  
  let result = await makeRequest('PUT', `/api/orders/${order._id}/status`, {
    status: 'processing'
  }, true);
  
  if (result.success) {
    console.log('✅ تم تحديث حالة الطلب بنجاح');
    console.log(`   الحالة الجديدة: ${result.data.status}`);
  } else {
    console.log('❌ فشل في تحديث حالة الطلب:', result.error);
  }
  
  // Test updating entire order
  console.log('\n🔸 اختبار تحديث الطلب بالكامل');
  
  result = await makeRequest('PUT', `/api/orders/${order._id}`, {
    notes: 'ملاحظة تجريبية - تم التحديث',
    quantity: 2
  }, true);
  
  if (result.success) {
    console.log('✅ تم تحديث الطلب بنجاح');
    console.log(`   الكمية الجديدة: ${result.data.quantity}`);
    console.log(`   الملاحظات الجديدة: ${result.data.notes}`);
  } else {
    console.log('❌ فشل في تحديث الطلب:', result.error);
  }
}

async function runCompleteTest() {
  try {
    console.log('🚀 بدء الاختبار الشامل لإنشاء الطلبات من المحادثة...');
    
    // Connect to database
    await database.connect();
    console.log('✅ تم الاتصال بقاعدة البيانات');
    
    // Setup test environment
    const userSetup = await setupTestUser();
    if (!userSetup) {
      throw new Error('Failed to setup test user');
    }
    
    await setupTestProducts();
    await setupStoreInfo();
    
    // Test chat order creation
    await testChatOrderCreation();
    
    // Test orders API
    const orders = await testOrdersAPI();
    
    // Test order management
    await testOrderManagement(orders);
    
    console.log('\n🎉 تم إنجاز جميع الاختبارات بنجاح!');
    
    // Summary
    console.log('\n📊 ملخص النتائج:');
    console.log('   ✅ إعداد البيئة التجريبية');
    console.log('   ✅ اختبار إنشاء الطلبات من المحادثة');
    console.log('   ✅ اختبار API الطلبات');
    console.log('   ✅ اختبار إدارة الطلبات');
    
  } catch (error) {
    console.error('\n❌ فشل في تشغيل الاختبار:', error.message);
  } finally {
    // Disconnect from database
    await database.disconnect();
    console.log('✅ تم قطع الاتصال بقاعدة البيانات');
    process.exit(0);
  }
}

// Run the complete test
runCompleteTest();
