require('dotenv').config();
const axios = require('axios');
const mongoose = require('mongoose');
const Order = require('../models/Order');

const BASE_URL = 'http://localhost:3000';
let authToken = null;

// Test user credentials
const testUser = {
  username: 'addresstest_' + Date.now(),
  password: 'testpassword123',
  email: '<EMAIL>',
  name: 'Address Test User'
};

async function makeRequest(method, endpoint, data = null, useAuth = false) {
  try {
    const config = {
      method,
      url: `${BASE_URL}${endpoint}`,
      headers: {}
    };

    if (useAuth && authToken) {
      config.headers['x-auth-token'] = authToken;
    }

    if (data) {
      config.data = data;
      config.headers['Content-Type'] = 'application/json';
    }

    const response = await axios(config);
    return { success: true, data: response.data, status: response.status };
  } catch (error) {
    return {
      success: false,
      error: error.response ? error.response.data : error.message,
      status: error.response ? error.response.status : 500
    };
  }
}

async function testCustomerAddressDisplay() {
  try {
    console.log('🔍 اختبار عرض عنوان العميل في الواجهة الأمامية...');
    console.log('=' .repeat(60));
    
    // Connect to database
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('✅ تم الاتصال بقاعدة البيانات');
    
    // Register test user
    const userResult = await makeRequest('POST', '/api/auth/register', testUser);
    if (!userResult.success) {
      throw new Error('Failed to create test user');
    }
    
    authToken = userResult.data.token;
    console.log('✅ تم إنشاء مستخدم الاختبار');
    
    // Create test orders with complete customer information
    console.log('\n📦 إنشاء طلبات اختبار مع عناوين مختلفة...');
    
    const testOrders = [
      {
        productName: 'آيفون 15 برو',
        quantity: 1,
        customerName: 'أحمد محمد علي',
        customerPhone: '07901234567',
        customerAddress: 'بغداد، الكرادة، شارع أبو نواس، بناية رقم 123',
        totalAmount: '1200',
        source: 'manual',
        notes: 'طلب اختبار للعنوان الطويل'
      },
      {
        productName: 'آيربودز برو',
        quantity: 2,
        customerName: 'فاطمة سالم',
        customerPhone: '07801234567',
        customerAddress: 'البصرة، المعقل',
        totalAmount: '500',
        source: 'api',
        notes: 'طلب اختبار للعنوان القصير'
      },
      {
        productName: 'آبل ووتش',
        quantity: 1,
        customerName: 'محمد حسن',
        customerPhone: '07701234567',
        customerAddress: 'أربيل، عنكاوا، مجمع الأندلس، الطابق الثالث',
        totalAmount: '400',
        source: 'chat',
        notes: 'طلب اختبار للعنوان المفصل'
      }
    ];
    
    const createdOrders = [];
    for (const order of testOrders) {
      const result = await makeRequest('POST', '/api/orders', order, true);
      if (result.success) {
        createdOrders.push(result.data);
        console.log(`✅ تم إنشاء طلب: ${order.productName} - العنوان: ${order.customerAddress}`);
      } else {
        console.log(`❌ فشل في إنشاء طلب: ${order.productName}`);
      }
    }
    
    // Get all orders via API to test frontend data
    console.log('\n📋 اختبار استرجاع الطلبات عبر API...');
    const ordersResult = await makeRequest('GET', '/api/orders', null, true);
    
    if (ordersResult.success) {
      console.log(`✅ تم استرجاع ${ordersResult.data.length} طلب`);
      
      // Test the customer info formatting logic (same as frontend)
      console.log('\n🔍 اختبار منطق تنسيق معلومات العميل:');
      
      ordersResult.data.slice(0, 5).forEach((order, index) => {
        console.log(`\nالطلب ${index + 1}:`);
        console.log(`  المنتج: ${order.productName || 'غير محدد'}`);
        console.log(`  الاسم: ${order.customerName || 'غير محدد'}`);
        console.log(`  الهاتف: ${order.customerPhone || 'غير محدد'}`);
        console.log(`  العنوان: ${order.customerAddress || 'غير محدد'}`);
        
        // Apply the same logic as frontend
        let customerInfo = '';
        if (order.customerName || order.customerPhone || order.customerAddress) {
          const parts = [];
          if (order.customerName) parts.push(`الاسم: ${order.customerName}`);
          if (order.customerPhone) parts.push(`الهاتف: ${order.customerPhone}`);
          if (order.customerAddress) parts.push(`العنوان: ${order.customerAddress}`);
          customerInfo = parts.join('<br>');
        } else if (order.customerInfo) {
          customerInfo = order.customerInfo;
        } else {
          customerInfo = '-';
        }
        
        console.log(`  معلومات العميل المنسقة:`);
        console.log(`    ${customerInfo.replace(/<br>/g, ' | ')}`);
        
        // Check if address is included
        if (customerInfo.includes('العنوان:')) {
          console.log(`  ✅ العنوان موجود في المعلومات المنسقة`);
        } else {
          console.log(`  ❌ العنوان مفقود من المعلومات المنسقة`);
        }
      });
      
      // Check for orders missing customerAddress
      const ordersWithoutAddress = ordersResult.data.filter(order => !order.customerAddress);
      console.log(`\n📊 إحصائيات العناوين:`);
      console.log(`  طلبات مع عنوان: ${ordersResult.data.length - ordersWithoutAddress.length}`);
      console.log(`  طلبات بدون عنوان: ${ordersWithoutAddress.length}`);
      
      if (ordersWithoutAddress.length > 0) {
        console.log(`\n⚠️ طلبات بدون عنوان:`);
        ordersWithoutAddress.forEach((order, index) => {
          console.log(`  ${index + 1}. ${order.productName || 'غير محدد'} - العميل: ${order.customerName || 'غير محدد'}`);
        });
      }
      
    } else {
      console.log('❌ فشل في استرجاع الطلبات');
    }
    
    // Test direct database query
    console.log('\n🗄️ اختبار الاستعلام المباشر من قاعدة البيانات...');
    const dbOrders = await Order.find().lean().limit(3);
    
    dbOrders.forEach((order, index) => {
      console.log(`\nطلب ${index + 1} من قاعدة البيانات:`);
      console.log(`  ID: ${order._id}`);
      console.log(`  المنتج: ${order.productName || 'غير محدد'}`);
      console.log(`  الاسم: ${order.customerName || 'غير محدد'}`);
      console.log(`  الهاتف: ${order.customerPhone || 'غير محدد'}`);
      console.log(`  العنوان: ${order.customerAddress || 'غير محدد'}`);
      
      if (order.customerAddress) {
        console.log(`  ✅ العنوان موجود في قاعدة البيانات`);
      } else {
        console.log(`  ❌ العنوان مفقود من قاعدة البيانات`);
      }
    });
    
    console.log('\n' + '=' .repeat(60));
    console.log('📋 خلاصة الاختبار:');
    
    if (createdOrders.length === testOrders.length) {
      console.log('✅ تم إنشاء جميع الطلبات التجريبية بنجاح');
    } else {
      console.log('⚠️ بعض الطلبات التجريبية لم يتم إنشاؤها');
    }
    
    if (ordersResult.success) {
      console.log('✅ API يسترجع الطلبات بنجاح');
    } else {
      console.log('❌ مشكلة في استرجاع الطلبات عبر API');
    }
    
    console.log('\n💡 التوصيات:');
    console.log('1. تحقق من أن الواجهة الأمامية تستخدم نفس منطق التنسيق');
    console.log('2. تأكد من أن العنوان يظهر في عمود "معلومات العميل"');
    console.log('3. افحص console في المتصفح للتأكد من عدم وجود أخطاء JavaScript');
    
  } catch (error) {
    console.error('\n❌ خطأ في الاختبار:', error.message);
  } finally {
    await mongoose.disconnect();
    console.log('\n✅ تم قطع الاتصال بقاعدة البيانات');
    process.exit(0);
  }
}

testCustomerAddressDisplay();
