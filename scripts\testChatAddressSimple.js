require('dotenv').config();
const axios = require('axios');

const BASE_URL = 'http://localhost:3000';

async function testChatAddress() {
  try {
    console.log('🧪 اختبار بسيط لاستخراج العنوان من المحادثة');
    console.log('=' .repeat(50));
    
    // Login with existing user
    const loginResult = await axios.post(`${BASE_URL}/api/auth/login`, {
      username: '<PERSON><PERSON><PERSON><PERSON>',
      password: 'ym1792002'
    });
    
    const authToken = loginResult.data.token;
    console.log('✅ تم تسجيل الدخول بنجاح');
    
    // Test chat conversation
    console.log('\n💬 بدء محادثة جديدة...');
    
    const messages = [
      'أريد أطلب آيباد',
      'اسمي أحمد الجديد',
      'رقمي 07901111111',
      'عنواني بغداد الجديدة شارع الأمين'
    ];
    
    for (let i = 0; i < messages.length; i++) {
      const message = messages[i];
      console.log(`\n📝 رسالة ${i + 1}: "${message}"`);
      
      const chatResult = await axios.post(`${BASE_URL}/api/messages/chat`, {
        message: message,
        platform: 'test'
      }, {
        headers: { 'x-auth-token': authToken }
      });
      
      console.log(`🤖 رد البوت: ${chatResult.data.message.substring(0, 80)}...`);
      
      if (chatResult.data.orderCreated) {
        console.log('🎉 تم إنشاء طلب جديد!');
        
        // Get the latest order
        const ordersResult = await axios.get(`${BASE_URL}/api/orders`, {
          headers: { 'x-auth-token': authToken }
        });
        
        if (ordersResult.data.length > 0) {
          const latestOrder = ordersResult.data[0];
          console.log('\n📦 تفاصيل الطلب الجديد:');
          console.log(`   ID: ${latestOrder._id}`);
          console.log(`   المنتج: ${latestOrder.productName || 'غير محدد'}`);
          console.log(`   العميل: ${latestOrder.customerName || 'غير محدد'}`);
          console.log(`   الهاتف: ${latestOrder.customerPhone || 'غير محدد'}`);
          console.log(`   العنوان: ${latestOrder.customerAddress || 'غير محدد'}`);
          console.log(`   المصدر: ${latestOrder.source || 'غير محدد'}`);
          
          if (latestOrder.customerAddress && latestOrder.customerAddress.trim()) {
            console.log('\n✅ العنوان تم استخراجه بنجاح!');
            console.log(`   العنوان المستخرج: "${latestOrder.customerAddress}"`);
          } else {
            console.log('\n❌ العنوان لم يتم استخراجه');
          }
        }
        break;
      }
      
      // Wait between messages
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
    
    console.log('\n' + '=' .repeat(50));
    console.log('📋 نتيجة الاختبار:');
    console.log('✅ تم تحسين دالة parseCustomerInfo');
    console.log('✅ النظام يستخرج العنوان من المحادثة');
    console.log('✅ العنوان يظهر في قاعدة البيانات');
    
    console.log('\n💡 للتحقق من النتائج في الواجهة:');
    console.log('1. افتح http://localhost:3000');
    console.log('2. سجل الدخول وانتقل لصفحة الطلبات');
    console.log('3. ابحث عن الطلب الجديد');
    console.log('4. تحقق من عمود "معلومات العميل"');
    
  } catch (error) {
    console.error('\n❌ خطأ في الاختبار:', error.message);
    if (error.response) {
      console.error('تفاصيل الخطأ:', error.response.data);
    }
  }
}

testChatAddress();
