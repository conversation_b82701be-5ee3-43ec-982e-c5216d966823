require('dotenv').config();
const axios = require('axios');

const BASE_URL = 'http://localhost:3000';

// Use existing user credentials
const existingUser = {
  username: 'el<PERSON><PERSON><PERSON>',
  password: 'ym1792002'
};

async function makeRequest(method, endpoint, data = null, useAuth = false, token = null) {
  try {
    const config = {
      method,
      url: `${BASE_URL}${endpoint}`,
      headers: {}
    };

    if (useAuth && token) {
      config.headers['x-auth-token'] = token;
    }

    if (data) {
      config.data = data;
      config.headers['Content-Type'] = 'application/json';
    }

    const response = await axios(config);
    return { success: true, data: response.data, status: response.status };
  } catch (error) {
    return {
      success: false,
      error: error.response ? error.response.data : error.message,
      status: error.response ? error.response.status : 500
    };
  }
}

async function finalCustomerAddressTest() {
  try {
    console.log('🎯 الاختبار النهائي لعرض عنوان العميل');
    console.log('=' .repeat(60));
    
    // Login
    console.log('🔐 تسجيل الدخول...');
    const loginResult = await makeRequest('POST', '/api/auth/login', existingUser);
    
    if (!loginResult.success) {
      console.log('❌ فشل في تسجيل الدخول:', loginResult.error);
      return;
    }
    
    const authToken = loginResult.data.token;
    console.log('✅ تم تسجيل الدخول بنجاح');
    
    // Create multiple test orders with different address scenarios
    console.log('\n📦 إنشاء طلبات اختبار متنوعة...');
    
    const testOrders = [
      {
        productName: 'اختبار العنوان الطويل',
        quantity: 1,
        customerName: 'أحمد محمد علي الحسيني',
        customerPhone: '07901234567',
        customerAddress: 'العراق، بغداد، منطقة الكرادة الشرقية، شارع أبو نواس، مجمع الواحة التجاري، الطابق الثاني، محل رقم 25، بجانب مطعم الأصالة',
        totalAmount: '1500',
        source: 'manual'
      },
      {
        productName: 'اختبار العنوان القصير',
        quantity: 2,
        customerName: 'فاطمة سالم',
        customerPhone: '07801234567',
        customerAddress: 'البصرة، المعقل',
        totalAmount: '800',
        source: 'api'
      },
      {
        productName: 'اختبار بدون عنوان',
        quantity: 1,
        customerName: 'محمد حسن',
        customerPhone: '07701234567',
        customerAddress: '', // عنوان فارغ
        totalAmount: '600',
        source: 'chat'
      },
      {
        productName: 'اختبار عنوان بأحرف خاصة',
        quantity: 3,
        customerName: 'سارة أحمد',
        customerPhone: '07601234567',
        customerAddress: 'أربيل - عنكاوا / مجمع الأندلس (الطابق الثالث) شقة #15',
        totalAmount: '2100',
        source: 'manual'
      }
    ];
    
    const createdOrders = [];
    for (let i = 0; i < testOrders.length; i++) {
      const order = testOrders[i];
      console.log(`\n📋 إنشاء الطلب ${i + 1}: ${order.productName}`);
      
      const result = await makeRequest('POST', '/api/orders', order, true, authToken);
      if (result.success) {
        createdOrders.push(result.data);
        console.log(`✅ تم إنشاء الطلب بنجاح`);
        console.log(`   العميل: ${order.customerName}`);
        console.log(`   الهاتف: ${order.customerPhone}`);
        console.log(`   العنوان: ${order.customerAddress || 'لا يوجد'}`);
      } else {
        console.log(`❌ فشل في إنشاء الطلب: ${result.error}`);
      }
    }
    
    // Test the API response
    console.log('\n🔍 اختبار استرجاع الطلبات عبر API...');
    const ordersResult = await makeRequest('GET', '/api/orders', null, true, authToken);
    
    if (ordersResult.success) {
      console.log(`✅ تم استرجاع ${ordersResult.data.length} طلب`);
      
      // Test the frontend formatting logic for each created order
      console.log('\n🎨 اختبار منطق تنسيق معلومات العميل:');
      
      const recentOrders = ordersResult.data
        .filter(order => createdOrders.some(created => created._id === order._id))
        .slice(0, 4);
      
      recentOrders.forEach((order, index) => {
        console.log(`\n--- الطلب ${index + 1}: ${order.productName} ---`);
        console.log(`البيانات الخام:`);
        console.log(`  customerName: "${order.customerName || ''}"`);
        console.log(`  customerPhone: "${order.customerPhone || ''}"`);
        console.log(`  customerAddress: "${order.customerAddress || ''}"`);
        
        // Apply the same logic as frontend (updated version)
        let customerInfo = '';
        
        if (order.customerName || order.customerPhone || order.customerAddress) {
          const parts = [];
          
          if (order.customerName && order.customerName.trim()) {
            parts.push(`<strong>الاسم:</strong> ${order.customerName.trim()}`);
          }
          
          if (order.customerPhone && order.customerPhone.trim()) {
            parts.push(`<strong>الهاتف:</strong> ${order.customerPhone.trim()}`);
          }
          
          if (order.customerAddress && order.customerAddress.trim()) {
            parts.push(`<strong>العنوان:</strong> ${order.customerAddress.trim()}`);
          }
          
          customerInfo = parts.length > 0 ? parts.join('<br>') : '-';
          
        } else if (order.customerInfo && order.customerInfo.trim()) {
          customerInfo = order.customerInfo.trim();
        } else {
          customerInfo = '<span class="text-muted">لا توجد معلومات</span>';
        }
        
        console.log(`النتيجة المنسقة:`);
        const displayInfo = customerInfo.replace(/<br>/g, '\n  ').replace(/<[^>]*>/g, '');
        console.log(`  ${displayInfo}`);
        
        // Check components
        const hasName = customerInfo.includes('الاسم:');
        const hasPhone = customerInfo.includes('الهاتف:');
        const hasAddress = customerInfo.includes('العنوان:');
        
        console.log(`التحقق:`);
        console.log(`  الاسم: ${hasName ? '✅' : '❌'}`);
        console.log(`  الهاتف: ${hasPhone ? '✅' : '❌'}`);
        console.log(`  العنوان: ${hasAddress ? '✅' : '❌'}`);
        
        if (order.customerAddress && order.customerAddress.trim() && !hasAddress) {
          console.log(`  ⚠️ تحذير: العنوان موجود في البيانات لكن لا يظهر في التنسيق!`);
        }
      });
      
      // Summary
      console.log('\n📊 ملخص الاختبار:');
      const ordersWithAddress = recentOrders.filter(order => order.customerAddress && order.customerAddress.trim());
      const ordersWithoutAddress = recentOrders.filter(order => !order.customerAddress || !order.customerAddress.trim());
      
      console.log(`  طلبات مع عنوان: ${ordersWithAddress.length}/${recentOrders.length}`);
      console.log(`  طلبات بدون عنوان: ${ordersWithoutAddress.length}/${recentOrders.length}`);
      
      if (ordersWithAddress.length > 0) {
        console.log(`\n✅ الطلبات التي تحتوي على عنوان:`);
        ordersWithAddress.forEach((order, index) => {
          console.log(`  ${index + 1}. ${order.productName} - العنوان: ${order.customerAddress.substring(0, 50)}...`);
        });
      }
      
      if (ordersWithoutAddress.length > 0) {
        console.log(`\n⚠️ الطلبات التي لا تحتوي على عنوان:`);
        ordersWithoutAddress.forEach((order, index) => {
          console.log(`  ${index + 1}. ${order.productName}`);
        });
      }
      
    } else {
      console.log('❌ فشل في استرجاع الطلبات');
    }
    
    console.log('\n' + '=' .repeat(60));
    console.log('🎯 نتائج الاختبار النهائي:');
    console.log('=' .repeat(60));
    
    if (createdOrders.length === testOrders.length) {
      console.log('✅ تم إنشاء جميع الطلبات التجريبية بنجاح');
    } else {
      console.log(`⚠️ تم إنشاء ${createdOrders.length}/${testOrders.length} طلب فقط`);
    }
    
    console.log('\n📋 خطوات التحقق اليدوي:');
    console.log('1. افتح المتصفح على: http://localhost:3000');
    console.log('2. سجل الدخول بالمستخدم: elzaeem');
    console.log('3. انتقل إلى صفحة "الطلبات"');
    console.log('4. ابحث عن الطلبات الجديدة التي تبدأ بـ "اختبار"');
    console.log('5. تحقق من عمود "معلومات العميل" - يجب أن يظهر:');
    console.log('   - الاسم: [اسم العميل]');
    console.log('   - الهاتف: [رقم الهاتف]');
    console.log('   - العنوان: [عنوان العميل] (إذا كان موجوداً)');
    console.log('6. تحقق من أن العناوين الطويلة تظهر بشكل صحيح');
    console.log('7. تحقق من أن الطلبات بدون عنوان تظهر الاسم والهاتف فقط');
    
    console.log('\n🎨 التحسينات المطبقة:');
    console.log('✅ تنسيق محسن لمعلومات العميل مع تسميات واضحة');
    console.log('✅ عرض العنوان بشكل منفصل ومميز');
    console.log('✅ دعم العناوين الطويلة مع تنسيق مناسب');
    console.log('✅ معالجة الحالات الخاصة (عنوان فارغ، أحرف خاصة)');
    console.log('✅ تصميم متجاوب للشاشات المختلفة');
    
    console.log('\n🔧 إذا لم يظهر العنوان:');
    console.log('1. تحقق من console المتصفح للأخطاء');
    console.log('2. تأكد من تحديث الصفحة (Ctrl+F5)');
    console.log('3. تحقق من أن البيانات محفوظة في قاعدة البيانات');
    console.log('4. تأكد من أن API يرسل البيانات بشكل صحيح');
    
  } catch (error) {
    console.error('\n❌ خطأ في الاختبار:', error.message);
  }
}

finalCustomerAddressTest();
