# تحويل نظام تخزين البيانات إلى MongoDB

## نظرة عامة

تم تحويل نظام تخزين البيانات في تطبيق AutoReply من ملفات JSON إلى قاعدة بيانات MongoDB بنجاح. هذا التحويل يوفر:

- أداء أفضل للاستعلامات
- قابلية توسع أكبر
- أمان محسن للبيانات
- إدارة أفضل للعلاقات بين البيانات

## التغييرات المنجزة

### 1. إضافة مكتبة MongoDB
```bash
npm install mongoose
```

### 2. إنشاء نماذج البيانات (Models)
تم إنشاء النماذج التالية في مجلد `models/`:

- `User.js` - نموذج المستخدمين
- `Token.js` - نموذج رموز الوصول لفيسبوك
- `Message.js` - نموذج الرسائل
- `Product.js` - نموذج المنتجات
- `StoreInfo.js` - نموذج معلومات المتجر
- `Order.js` - نموذج الطلبات
- `ActivationCode.js` - نموذج رموز التفعيل

### 3. إعداد الاتصال بقاعدة البيانات
- إنشاء ملف `config/database.js` لإدارة الاتصال
- إضافة متغير البيئة `MONGODB_URI` في ملف `.env`

### 4. تحديث DataStore
تم تحديث ملف `utils/dataStore.js` ليستخدم MongoDB بدلاً من ملفات JSON مع الحفاظ على نفس واجهة برمجة التطبيقات.

### 5. تحديث الخادم
تم تحديث `server.js` لتهيئة الاتصال بقاعدة البيانات عند بدء التشغيل.

### 6. نقل البيانات
تم إنشاء سكريبت `scripts/migrateData.js` لنقل جميع البيانات من ملفات JSON إلى MongoDB.

## كيفية الاستخدام

### تشغيل التطبيق
```bash
node server.js
```

### نقل البيانات (إذا لزم الأمر)
```bash
node scripts/migrateData.js
```

### اختبار قاعدة البيانات
```bash
node scripts/testDatabase.js
```

## متغيرات البيئة

تأكد من إضافة متغير البيئة التالي في ملف `.env`:

```
MONGODB_URI=mongodb+srv://yousefmuhamedeng22:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0
```

## البيانات المنقولة

تم نقل البيانات التالية بنجاح:
- ✅ 2 مستخدمين
- ✅ 1 رمز وصول فيسبوك
- ✅ 384 رسالة (192 رسالة مكررة تم تجاهلها)
- ✅ 3 منتجات
- ✅ 2 ملف معلومات متجر
- ✅ 2 رمز تفعيل

## الميزات الجديدة

1. **فهرسة محسنة**: تم إضافة فهارس لتحسين أداء الاستعلامات
2. **التحقق من صحة البيانات**: تم إضافة قواعد التحقق في النماذج
3. **إدارة الاتصال**: إدارة تلقائية لاتصال قاعدة البيانات
4. **معالجة الأخطاء**: معالجة محسنة للأخطاء

## ملاحظات مهمة

1. **التوافق مع الإصدارات السابقة**: تم الحفاظ على نفس واجهة برمجة التطبيقات في DataStore
2. **معرفات البيانات**: تم استخدام UUID كمعرفات بدلاً من ObjectId للتوافق مع البيانات الموجودة
3. **النسخ الاحتياطي**: ملفات JSON الأصلية محفوظة في مجلد `data/`

## الاختبار

تم اختبار جميع العمليات التالية بنجاح:
- ✅ قراءة المستخدمين
- ✅ قراءة الرسائل
- ✅ قراءة المنتجات
- ✅ قراءة معلومات المتجر
- ✅ قراءة الطلبات
- ✅ قراءة رموز التفعيل
- ✅ قراءة رموز الوصول

## الدعم

في حالة وجود أي مشاكل، يمكن الرجوع إلى ملفات JSON الأصلية في مجلد `data/` كنسخة احتياطية.
