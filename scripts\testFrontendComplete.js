require('dotenv').config();
const axios = require('axios');
const database = require('../config/database');

const BASE_URL = 'http://localhost:3000';
let authToken = null;
let testUserId = null;

// Test data
const testUser = {
  username: 'frontendtest_' + Date.now(),
  password: 'testpassword123',
  email: '<EMAIL>',
  name: 'Frontend Test User'
};

async function makeRequest(method, endpoint, data = null, useAuth = false) {
  try {
    const config = {
      method,
      url: `${BASE_URL}${endpoint}`,
      headers: {}
    };

    if (useAuth && authToken) {
      config.headers['x-auth-token'] = authToken;
    }

    if (data) {
      config.data = data;
      config.headers['Content-Type'] = 'application/json';
    }

    const response = await axios(config);
    return { success: true, data: response.data, status: response.status };
  } catch (error) {
    return {
      success: false,
      error: error.response ? error.response.data : error.message,
      status: error.response ? error.response.status : 500
    };
  }
}

async function setupTestEnvironment() {
  console.log('\n🔧 إعداد البيئة التجريبية...');
  
  // Register user
  const userResult = await makeRequest('POST', '/api/auth/register', testUser);
  if (!userResult.success) {
    throw new Error('Failed to create test user');
  }
  
  authToken = userResult.data.token;
  testUserId = userResult.data.user.id;
  console.log('✅ تم إنشاء مستخدم الاختبار');
  
  // Add test products
  const products = [
    { name: 'آيفون 15 برو', price: '1200 دولار', description: 'هاتف ذكي متطور' },
    { name: 'آيربودز برو', price: '250 دولار', description: 'سماعات لاسلكية' },
    { name: 'آبل ووتش', price: '400 دولار', description: 'ساعة ذكية' },
    { name: 'ماك بوك برو', price: '2500 دولار', description: 'لابتوب متطور' }
  ];
  
  const addedProducts = [];
  for (const product of products) {
    const result = await makeRequest('POST', '/api/products', product, true);
    if (result.success) {
      addedProducts.push(result.data);
    }
  }
  console.log(`✅ تم إضافة ${addedProducts.length} منتج تجريبي`);
  
  // Setup store info
  const storeInfo = {
    name: 'متجر التكنولوجيا الشامل',
    address: 'شارع الحبيبية، بغداد، العراق',
    description: 'متجر متخصص في بيع أحدث المنتجات التكنولوجية والإلكترونية',
    phone: '+964 ************',
    email: '<EMAIL>'
  };
  
  await makeRequest('POST', '/api/store/info', storeInfo, true);
  console.log('✅ تم إعداد معلومات المتجر');
  
  return addedProducts;
}

async function testOrdersManagement() {
  console.log('\n📦 اختبار إدارة الطلبات...');
  
  let testsPassed = 0;
  let totalTests = 0;
  
  // Test 1: Create orders with different sources
  totalTests++;
  console.log('\n🔸 اختبار 1: إنشاء طلبات من مصادر مختلفة');
  
  const orders = [
    {
      productName: 'آيفون 15 برو',
      quantity: 1,
      customerName: 'أحمد محمد',
      customerPhone: '07901234567',
      customerAddress: 'بغداد، الكرادة',
      totalAmount: '1200',
      source: 'manual',
      notes: 'طلب يدوي للاختبار'
    },
    {
      productName: 'آيربودز برو',
      quantity: 2,
      customerName: 'فاطمة علي',
      customerPhone: '07801234567',
      customerAddress: 'البصرة، المعقل',
      totalAmount: '500',
      source: 'api',
      notes: 'طلب من API'
    }
  ];
  
  const createdOrders = [];
  for (const order of orders) {
    const result = await makeRequest('POST', '/api/orders', order, true);
    if (result.success) {
      createdOrders.push(result.data);
    }
  }
  
  if (createdOrders.length === orders.length) {
    console.log('✅ تم إنشاء جميع الطلبات بنجاح');
    testsPassed++;
  } else {
    console.log('❌ فشل في إنشاء بعض الطلبات');
  }
  
  // Test 2: Create orders from chat
  totalTests++;
  console.log('\n🔸 اختبار 2: إنشاء طلبات من المحادثة');
  
  const chatResult = await makeRequest('POST', '/api/messages/chat', {
    message: 'أريد شراء آبل ووتش، اسمي سارة أحمد، رقمي 07701234567، عنواني النجف',
    platform: 'test'
  }, true);
  
  if (chatResult.success && chatResult.data.orderCreated) {
    console.log('✅ تم إنشاء طلب من المحادثة بنجاح');
    testsPassed++;
  } else {
    console.log('❌ فشل في إنشاء طلب من المحادثة');
  }
  
  // Test 3: Get all orders and verify data
  totalTests++;
  console.log('\n🔸 اختبار 3: استرجاع الطلبات والتحقق من البيانات');
  
  const ordersResult = await makeRequest('GET', '/api/orders', null, true);
  
  if (ordersResult.success && ordersResult.data.length >= 3) {
    console.log(`✅ تم استرجاع ${ordersResult.data.length} طلب`);
    
    // Verify order data structure
    const order = ordersResult.data[0];
    const hasRequiredFields = order._id && order.productName && order.customerName && 
                             order.customerPhone && order.status && order.source;
    
    if (hasRequiredFields) {
      console.log('✅ جميع الحقول المطلوبة موجودة في الطلبات');
      testsPassed++;
    } else {
      console.log('❌ بعض الحقول المطلوبة مفقودة في الطلبات');
    }
    
    // Display sample order data
    console.log('\n📋 عينة من بيانات الطلب:');
    console.log(`   المنتج: ${order.productName || 'غير محدد'}`);
    console.log(`   العميل: ${order.customerName || 'غير محدد'}`);
    console.log(`   الهاتف: ${order.customerPhone || 'غير محدد'}`);
    console.log(`   العنوان: ${order.customerAddress || 'غير محدد'}`);
    console.log(`   المبلغ: ${order.totalAmount || 'غير محدد'}`);
    console.log(`   الحالة: ${order.status || 'غير محدد'}`);
    console.log(`   المصدر: ${order.source || 'غير محدد'}`);
    
  } else {
    console.log('❌ فشل في استرجاع الطلبات أو عدد الطلبات غير كافي');
  }
  
  // Test 4: Update order
  if (ordersResult.success && ordersResult.data.length > 0) {
    totalTests++;
    console.log('\n🔸 اختبار 4: تحديث الطلب');
    
    const orderId = ordersResult.data[0]._id;
    const updateData = {
      quantity: 3,
      customerPhone: '07901111111',
      notes: 'تم التحديث في الاختبار الشامل',
      status: 'processing'
    };
    
    const updateResult = await makeRequest('PUT', `/api/orders/${orderId}`, updateData, true);
    
    if (updateResult.success) {
      console.log('✅ تم تحديث الطلب بنجاح');
      console.log(`   الكمية الجديدة: ${updateResult.data.quantity}`);
      console.log(`   الهاتف الجديد: ${updateResult.data.customerPhone}`);
      console.log(`   الحالة الجديدة: ${updateResult.data.status}`);
      testsPassed++;
    } else {
      console.log('❌ فشل في تحديث الطلب');
    }
  }
  
  return { testsPassed, totalTests, orders: ordersResult.success ? ordersResult.data : [] };
}

async function testProductsManagement() {
  console.log('\n🛍️ اختبار إدارة المنتجات...');
  
  let testsPassed = 0;
  let totalTests = 0;
  
  // Test 1: Get all products
  totalTests++;
  console.log('\n🔸 اختبار 1: استرجاع المنتجات');
  
  const productsResult = await makeRequest('GET', '/api/products', null, true);
  
  if (productsResult.success && productsResult.data.length >= 4) {
    console.log(`✅ تم استرجاع ${productsResult.data.length} منتج`);
    testsPassed++;
    
    // Verify product data structure
    const product = productsResult.data[0];
    if (product._id && product.name && product.price) {
      console.log('✅ بنية بيانات المنتج صحيحة');
    } else {
      console.log('❌ بنية بيانات المنتج غير صحيحة');
    }
    
  } else {
    console.log('❌ فشل في استرجاع المنتجات');
  }
  
  // Test 2: Update product
  if (productsResult.success && productsResult.data.length > 0) {
    totalTests++;
    console.log('\n🔸 اختبار 2: تحديث المنتج');
    
    const productId = productsResult.data[0]._id;
    const updateData = {
      name: 'آيفون 15 برو - محدث',
      price: '1100 دولار',
      description: 'هاتف ذكي متطور - تم التحديث'
    };
    
    const updateResult = await makeRequest('PUT', `/api/products/${productId}`, updateData, true);
    
    if (updateResult.success) {
      console.log('✅ تم تحديث المنتج بنجاح');
      console.log(`   الاسم الجديد: ${updateResult.data.name}`);
      console.log(`   السعر الجديد: ${updateResult.data.price}`);
      testsPassed++;
    } else {
      console.log('❌ فشل في تحديث المنتج');
    }
  }
  
  // Test 3: Add new product
  totalTests++;
  console.log('\n🔸 اختبار 3: إضافة منتج جديد');
  
  const newProduct = {
    name: 'آيباد برو',
    price: '800 دولار',
    description: 'تابلت متطور للمحترفين'
  };
  
  const addResult = await makeRequest('POST', '/api/products', newProduct, true);
  
  if (addResult.success) {
    console.log('✅ تم إضافة المنتج الجديد بنجاح');
    console.log(`   ID: ${addResult.data._id}`);
    console.log(`   الاسم: ${addResult.data.name}`);
    testsPassed++;
  } else {
    console.log('❌ فشل في إضافة المنتج الجديد');
  }
  
  return { testsPassed, totalTests };
}

async function testSystemIntegration() {
  console.log('\n🔗 اختبار تكامل النظام...');
  
  let testsPassed = 0;
  let totalTests = 0;
  
  // Test 1: Chat functionality
  totalTests++;
  console.log('\n🔸 اختبار 1: وظائف المحادثة');
  
  const chatResult = await makeRequest('POST', '/api/messages/chat', {
    message: 'مرحبا، ما هي المنتجات المتوفرة؟',
    platform: 'test'
  }, true);
  
  if (chatResult.success && chatResult.data.message) {
    console.log('✅ المحادثة تعمل بنجاح');
    console.log(`   الرد: ${chatResult.data.message.substring(0, 50)}...`);
    testsPassed++;
  } else {
    console.log('❌ فشل في المحادثة');
  }
  
  // Test 2: Message statistics
  totalTests++;
  console.log('\n🔸 اختبار 2: إحصائيات الرسائل');
  
  const statsResult = await makeRequest('GET', '/api/messages/stats', null, true);
  
  if (statsResult.success && statsResult.data.totalMessages >= 0) {
    console.log(`✅ تم الحصول على إحصائيات الرسائل: ${statsResult.data.totalMessages} رسالة`);
    testsPassed++;
  } else {
    console.log('❌ فشل في الحصول على إحصائيات الرسائل');
  }
  
  // Test 3: Store information
  totalTests++;
  console.log('\n🔸 اختبار 3: معلومات المتجر');
  
  const storeResult = await makeRequest('GET', '/api/store/info', null, true);
  
  if (storeResult.success && storeResult.data.name) {
    console.log(`✅ تم استرجاع معلومات المتجر: ${storeResult.data.name}`);
    testsPassed++;
  } else {
    console.log('❌ فشل في استرجاع معلومات المتجر');
  }
  
  return { testsPassed, totalTests };
}

async function runFrontendCompleteTest() {
  try {
    console.log('🚀 بدء الاختبار الشامل للواجهة الأمامية...');
    console.log('=' .repeat(60));
    
    // Connect to database
    await database.connect();
    console.log('✅ تم الاتصال بقاعدة البيانات');
    
    // Setup test environment
    await setupTestEnvironment();
    
    // Test orders management
    const ordersResults = await testOrdersManagement();
    
    // Test products management
    const productsResults = await testProductsManagement();
    
    // Test system integration
    const integrationResults = await testSystemIntegration();
    
    // Calculate final results
    const totalPassed = ordersResults.testsPassed + productsResults.testsPassed + integrationResults.testsPassed;
    const totalTests = ordersResults.totalTests + productsResults.totalTests + integrationResults.totalTests;
    const successRate = ((totalPassed / totalTests) * 100).toFixed(1);
    
    console.log('\n' + '=' .repeat(60));
    console.log('📊 النتائج النهائية للواجهة الأمامية:');
    console.log('=' .repeat(60));
    
    console.log(`\n📦 اختبارات إدارة الطلبات:`);
    console.log(`   نجح: ${ordersResults.testsPassed}/${ordersResults.totalTests}`);
    
    console.log(`\n🛍️ اختبارات إدارة المنتجات:`);
    console.log(`   نجح: ${productsResults.testsPassed}/${productsResults.totalTests}`);
    
    console.log(`\n🔗 اختبارات تكامل النظام:`);
    console.log(`   نجح: ${integrationResults.testsPassed}/${integrationResults.totalTests}`);
    
    console.log(`\n📈 النتيجة الإجمالية:`);
    console.log(`   نجح: ${totalPassed}/${totalTests} (${successRate}%)`);
    
    if (successRate >= 90) {
      console.log('\n🎉 جميع اختبارات الواجهة الأمامية نجحت!');
      console.log('✅ التطبيق جاهز للنشر على الإنترنت');
    } else {
      console.log('\n⚠️ بعض الاختبارات فشلت، يحتاج مراجعة');
    }
    
    console.log('\n🔧 الميزات المختبرة:');
    console.log('   ✅ عرض معلومات العميل بالتفصيل');
    console.log('   ✅ تعديل الطلبات بالكامل');
    console.log('   ✅ تحديث حالة الطلبات');
    console.log('   ✅ إدارة المنتجات (إضافة، تعديل، حذف)');
    console.log('   ✅ تتبع مصدر الطلبات');
    console.log('   ✅ المحادثة وإنشاء الطلبات');
    console.log('   ✅ إحصائيات النظام');
    
  } catch (error) {
    console.error('\n❌ فشل في تشغيل الاختبار الشامل:', error.message);
  } finally {
    // Disconnect from database
    await database.disconnect();
    console.log('\n✅ تم قطع الاتصال بقاعدة البيانات');
    console.log('\n🏁 انتهى الاختبار الشامل للواجهة الأمامية');
    process.exit(0);
  }
}

// Run the frontend complete test
runFrontendCompleteTest();
