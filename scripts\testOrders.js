require('dotenv').config();
const axios = require('axios');

const BASE_URL = 'http://localhost:3001';
let authToken = null;

// Test data
const testUser = {
  username: 'ordertest_' + Date.now(),
  password: 'testpassword123',
  email: '<EMAIL>',
  name: 'Order Test User'
};

async function makeRequest(method, endpoint, data = null, useAuth = false) {
  try {
    const config = {
      method,
      url: `${BASE_URL}${endpoint}`,
      headers: {}
    };

    if (useAuth && authToken) {
      config.headers['x-auth-token'] = authToken;
    }

    if (data) {
      config.data = data;
      config.headers['Content-Type'] = 'application/json';
    }

    const response = await axios(config);
    return { success: true, data: response.data, status: response.status };
  } catch (error) {
    return {
      success: false,
      error: error.response ? error.response.data : error.message,
      status: error.response ? error.response.status : 500
    };
  }
}

async function setupUser() {
  console.log('إنشاء مستخدم جديد...');
  const result = await makeRequest('POST', '/api/auth/register', testUser);
  
  if (result.success) {
    console.log('✅ تم إنشاء المستخدم بنجاح');
    authToken = result.data.token;
    return true;
  } else {
    console.log('❌ فشل في إنشاء المستخدم:', result.error);
    return false;
  }
}

async function testOrderCreation() {
  console.log('\n--- اختبار إنشاء الطلبات ---');
  
  const orders = [
    {
      productId: 'test-product-1',
      productName: 'آيفون 15 برو',
      quantity: 1,
      customerName: 'أحمد محمد',
      customerPhone: '07901234567',
      customerAddress: 'بغداد، العراق',
      totalAmount: '1100',
      notes: 'يفضل التسليم مساءً'
    },
    {
      productId: 'test-product-2',
      productName: 'آيربودز برو',
      quantity: 2,
      customerName: 'فاطمة علي',
      customerPhone: '07801234567',
      customerAddress: 'البصرة، العراق',
      totalAmount: '500',
      notes: 'لون أبيض'
    }
  ];
  
  const addedOrders = [];
  
  for (const order of orders) {
    console.log(`إضافة طلب: ${order.productName}...`);
    const result = await makeRequest('POST', '/api/orders', order, true);
    
    if (result.success) {
      addedOrders.push(result.data);
      console.log(`✅ تم إضافة الطلب بنجاح - ID: ${result.data._id}`);
      console.log(`   العميل: ${result.data.customerName}`);
      console.log(`   المنتج: ${result.data.productName}`);
      console.log(`   الكمية: ${result.data.quantity}`);
      console.log(`   المبلغ: ${result.data.totalAmount}`);
    } else {
      console.log(`❌ فشل في إضافة الطلب: ${result.error.message || result.error}`);
    }
  }
  
  return addedOrders;
}

async function testOrderRetrieval() {
  console.log('\n--- اختبار استرجاع الطلبات ---');
  
  const result = await makeRequest('GET', '/api/orders', null, true);
  
  if (result.success) {
    console.log(`✅ تم استرجاع ${result.data.length} طلب`);
    
    result.data.forEach((order, index) => {
      console.log(`   الطلب ${index + 1}:`);
      console.log(`     ID: ${order._id}`);
      console.log(`     المنتج: ${order.productName}`);
      console.log(`     العميل: ${order.customerName}`);
      console.log(`     الحالة: ${order.status}`);
      console.log(`     التاريخ: ${new Date(order.createdAt).toLocaleString('ar-EG')}`);
    });
    
    return result.data;
  } else {
    console.log('❌ فشل في استرجاع الطلبات:', result.error);
    return [];
  }
}

async function testOrderStatusUpdate(orders) {
  console.log('\n--- اختبار تحديث حالة الطلبات ---');
  
  if (orders.length === 0) {
    console.log('لا توجد طلبات لتحديثها');
    return;
  }
  
  const order = orders[0];
  console.log(`تحديث حالة الطلب: ${order._id}...`);
  
  const result = await makeRequest('PUT', `/api/orders/${order._id}/status`, {
    status: 'processing'
  }, true);
  
  if (result.success) {
    console.log('✅ تم تحديث حالة الطلب بنجاح');
    console.log(`   الحالة الجديدة: ${result.data.status}`);
  } else {
    console.log('❌ فشل في تحديث حالة الطلب:', result.error);
  }
}

async function testOrderDeletion(orders) {
  console.log('\n--- اختبار حذف الطلبات ---');
  
  if (orders.length < 2) {
    console.log('لا توجد طلبات كافية للحذف');
    return;
  }
  
  const order = orders[1];
  console.log(`حذف الطلب: ${order._id}...`);
  
  const result = await makeRequest('DELETE', `/api/orders/${order._id}`, null, true);
  
  if (result.success) {
    console.log('✅ تم حذف الطلب بنجاح');
  } else {
    console.log('❌ فشل في حذف الطلب:', result.error);
  }
}

async function runOrderTests() {
  try {
    console.log('🚀 بدء اختبار نظام الطلبات...');
    
    // Setup user
    const userSetup = await setupUser();
    if (!userSetup) {
      throw new Error('Failed to setup user');
    }
    
    // Test order creation
    const addedOrders = await testOrderCreation();
    
    // Test order retrieval
    const retrievedOrders = await testOrderRetrieval();
    
    // Test order status update
    await testOrderStatusUpdate(retrievedOrders);
    
    // Test order deletion
    await testOrderDeletion(retrievedOrders);
    
    // Final check
    await testOrderRetrieval();
    
    console.log('\n🎉 تم إنجاز جميع اختبارات الطلبات بنجاح!');
    
  } catch (error) {
    console.error('\n❌ فشل في تشغيل اختبارات الطلبات:', error.message);
  } finally {
    process.exit(0);
  }
}

// Run the tests
runOrderTests();
