require('dotenv').config();
const axios = require('axios');

const BASE_URL = 'http://localhost:3000';

// Use existing user credentials
const existingUser = {
  username: 'el<PERSON><PERSON><PERSON>',
  password: 'ym1792002'
};

async function makeRequest(method, endpoint, data = null, useAuth = false, token = null) {
  try {
    const config = {
      method,
      url: `${BASE_URL}${endpoint}`,
      headers: {}
    };

    if (useAuth && token) {
      config.headers['x-auth-token'] = token;
    }

    if (data) {
      config.data = data;
      config.headers['Content-Type'] = 'application/json';
    }

    const response = await axios(config);
    return { success: true, data: response.data, status: response.status };
  } catch (error) {
    return {
      success: false,
      error: error.response ? error.response.data : error.message,
      status: error.response ? error.response.status : 500
    };
  }
}

async function createTestOrderWithAddress() {
  try {
    console.log('🧪 إنشاء طلب اختبار مع عنوان مفصل...');
    console.log('=' .repeat(50));
    
    // Login with existing user
    console.log('🔐 تسجيل الدخول...');
    const loginResult = await makeRequest('POST', '/api/auth/login', existingUser);
    
    if (!loginResult.success) {
      console.log('❌ فشل في تسجيل الدخول:', loginResult.error);
      return;
    }
    
    const authToken = loginResult.data.token;
    console.log('✅ تم تسجيل الدخول بنجاح');
    
    // Create a test order with detailed address
    console.log('\n📦 إنشاء طلب اختبار...');
    const testOrder = {
      productName: 'آيفون 15 برو ماكس - اختبار العنوان',
      quantity: 1,
      customerName: 'سارة أحمد محمد',
      customerPhone: '07901234567',
      customerAddress: 'بغداد، منطقة الكرادة، شارع أبو نواس، مجمع الواحة التجاري، الطابق الثاني، محل رقم 25',
      totalAmount: '1300',
      source: 'manual',
      notes: 'طلب اختبار لفحص عرض العنوان في الواجهة الأمامية - تم إنشاؤه في ' + new Date().toLocaleString('ar-IQ')
    };
    
    const orderResult = await makeRequest('POST', '/api/orders', testOrder, true, authToken);
    
    if (orderResult.success) {
      console.log('✅ تم إنشاء الطلب بنجاح');
      console.log('📋 تفاصيل الطلب:');
      console.log(`   ID: ${orderResult.data._id}`);
      console.log(`   المنتج: ${orderResult.data.productName}`);
      console.log(`   العميل: ${orderResult.data.customerName}`);
      console.log(`   الهاتف: ${orderResult.data.customerPhone}`);
      console.log(`   العنوان: ${orderResult.data.customerAddress}`);
      console.log(`   المصدر: ${orderResult.data.source}`);
      
      // Verify the order was saved correctly
      console.log('\n🔍 التحقق من حفظ الطلب...');
      const ordersResult = await makeRequest('GET', '/api/orders', null, true, authToken);
      
      if (ordersResult.success) {
        const createdOrder = ordersResult.data.find(order => order._id === orderResult.data._id);
        
        if (createdOrder) {
          console.log('✅ تم العثور على الطلب في قائمة الطلبات');
          console.log('📋 بيانات الطلب المسترجعة:');
          console.log(`   الاسم: ${createdOrder.customerName || 'غير موجود'}`);
          console.log(`   الهاتف: ${createdOrder.customerPhone || 'غير موجود'}`);
          console.log(`   العنوان: ${createdOrder.customerAddress || 'غير موجود'}`);
          
          // Test the frontend formatting logic
          console.log('\n🎨 اختبار منطق التنسيق (نفس الواجهة الأمامية):');
          let customerInfo = '';
          if (createdOrder.customerName || createdOrder.customerPhone || createdOrder.customerAddress) {
            const parts = [];
            if (createdOrder.customerName) parts.push(`الاسم: ${createdOrder.customerName}`);
            if (createdOrder.customerPhone) parts.push(`الهاتف: ${createdOrder.customerPhone}`);
            if (createdOrder.customerAddress) parts.push(`العنوان: ${createdOrder.customerAddress}`);
            customerInfo = parts.join('<br>');
          } else {
            customerInfo = '-';
          }
          
          console.log('📝 النتيجة المنسقة:');
          console.log(customerInfo.replace(/<br>/g, '\n   '));
          
          if (customerInfo.includes('العنوان:')) {
            console.log('\n✅ العنوان موجود في المعلومات المنسقة');
          } else {
            console.log('\n❌ العنوان مفقود من المعلومات المنسقة');
          }
          
        } else {
          console.log('❌ لم يتم العثور على الطلب في قائمة الطلبات');
        }
      } else {
        console.log('❌ فشل في استرجاع قائمة الطلبات');
      }
      
    } else {
      console.log('❌ فشل في إنشاء الطلب:', orderResult.error);
    }
    
    console.log('\n' + '=' .repeat(50));
    console.log('📋 تعليمات الاختبار اليدوي:');
    console.log('1. افتح المتصفح على http://localhost:3000');
    console.log('2. سجل الدخول بالمستخدم: elzaeem');
    console.log('3. انتقل إلى صفحة "الطلبات"');
    console.log('4. ابحث عن الطلب الجديد: "آيفون 15 برو ماكس - اختبار العنوان"');
    console.log('5. تحقق من عمود "معلومات العميل" - يجب أن يظهر:');
    console.log('   - الاسم: سارة أحمد محمد');
    console.log('   - الهاتف: 07901234567');
    console.log('   - العنوان: بغداد، منطقة الكرادة، شارع أبو نواس...');
    console.log('6. افتح Developer Tools (F12) وتحقق من Console للرسائل');
    
    console.log('\n💡 إذا لم يظهر العنوان:');
    console.log('1. تحقق من رسائل console في المتصفح');
    console.log('2. تأكد من أن البيانات تصل بشكل صحيح من API');
    console.log('3. تحقق من أن منطق التنسيق يعمل بشكل صحيح');
    
  } catch (error) {
    console.error('❌ خطأ في الاختبار:', error.message);
  }
}

createTestOrderWithAddress();
