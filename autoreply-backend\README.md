# AutoReply Backend API Server

Backend API server for AutoReply - Smart E-commerce Customer Service Automation System.

## Features

- **RESTful API**: Complete API for AutoReply frontend
- **MongoDB Integration**: Database operations with Mongoose
- **AI Chat**: Google Gemini AI integration
- **Authentication**: JWT-based authentication system
- **Admin Panel**: Admin routes and statistics
- **Facebook Integration**: OAuth and webhook support
- **CORS Enabled**: Cross-origin resource sharing configured

## Tech Stack

- **Node.js**: Runtime environment
- **Express.js**: Web framework
- **MongoDB**: Database with Mongoose ODM
- **JWT**: Authentication tokens
- **Google Gemini AI**: Chatbot intelligence
- **Facebook Graph API**: Social media integration

## Environment Variables

```env
# Database
MONGODB_URI=mongodb+srv://username:<EMAIL>/autoreply

# Security
JWT_SECRET=your-jwt-secret-key
SESSION_SECRET=your-session-secret-key

# AI
GEMINI_API_KEY=your-gemini-api-key

# Facebook (Optional)
FACEBOOK_APP_ID=your-facebook-app-id
FACEBOOK_APP_SECRET=your-facebook-app-secret

# Server
PORT=3000
NODE_ENV=production
FRONTEND_URL=https://your-frontend-url.com
```

## API Endpoints

### Authentication
- `POST /api/auth/register` - Register new user
- `POST /api/auth/login` - User login
- `GET /api/auth/me` - Get current user
- `POST /api/auth/admin/login` - Admin login

### Products
- `GET /api/products` - Get all products
- `POST /api/products` - Add new product
- `PUT /api/products/:id` - Update product
- `DELETE /api/products/:id` - Delete product

### Orders
- `GET /api/orders` - Get all orders
- `POST /api/orders` - Create new order
- `PUT /api/orders/:id` - Update order
- `DELETE /api/orders/:id` - Delete order

### Messages
- `POST /api/messages/chat` - Send message to AI
- `GET /api/messages` - Get message history
- `GET /api/messages/stats` - Get message statistics

### Admin
- `GET /api/admin/stats` - Get admin statistics
- `GET /api/admin/users` - Get all users
- `GET /api/admin/codes` - Get activation codes

### Store
- `GET /api/store/info` - Get store information
- `POST /api/store/info` - Update store information

## Installation

```bash
# Install dependencies
npm install

# Set up environment variables
cp .env.example .env
# Edit .env with your values

# Start development server
npm run dev

# Start production server
npm start
```

## Deployment

This backend is designed to be deployed on Railway.app:

1. Push code to GitHub repository
2. Connect Railway to your GitHub repo
3. Set environment variables in Railway dashboard
4. Deploy automatically

## Health Check

The server provides a health check endpoint:

```bash
GET /health
```

Response:
```json
{
  "status": "OK",
  "timestamp": "2025-05-29T00:00:00.000Z",
  "environment": "production"
}
```

## CORS Configuration

The server is configured to accept requests from:
- Firebase Hosting domains
- Local development (localhost)

## Security Features

- JWT token authentication
- Session management
- CORS protection
- Environment variable protection
- Input validation
- Error handling

## License

MIT License - see LICENSE file for details.

## Support

For support and questions, please refer to the main AutoReply repository.
#   a u t o r e p l y - b a c k e n d 
 
 