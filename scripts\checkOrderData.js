require('dotenv').config();
const mongoose = require('mongoose');
const Order = require('../models/Order');

async function checkOrderData() {
  try {
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('Connected to database');
    
    // Get all orders for debugging
    const orders = await Order.find().lean();
    console.log('Total orders found:', orders.length);
    
    if (orders.length > 0) {
      console.log('\nFirst few orders data:');
      orders.slice(0, 3).forEach((order, index) => {
        console.log(`\nOrder ${index + 1}:`);
        console.log('Order ID:', order._id);
        console.log('Product Name:', order.productName);
        console.log('Customer Name:', order.customerName);
        console.log('Customer Phone:', order.customerPhone);
        console.log('Customer Address:', order.customerAddress);
        console.log('Customer Info (legacy):', order.customerInfo);
        console.log('Source:', order.source);
        console.log('Status:', order.status);
        console.log('Created At:', order.createdAt);
        
        // Check if customerAddress exists and has value
        if (order.customerAddress) {
          console.log('✅ Customer Address is present:', order.customerAddress);
        } else {
          console.log('❌ Customer Address is missing or empty');
        }
      });
      
      // Check for orders with missing customerAddress
      const ordersWithoutAddress = orders.filter(order => !order.customerAddress);
      console.log(`\nOrders without customerAddress: ${ordersWithoutAddress.length}/${orders.length}`);
      
      if (ordersWithoutAddress.length > 0) {
        console.log('\nOrders missing customerAddress:');
        ordersWithoutAddress.forEach((order, index) => {
          console.log(`${index + 1}. Order ID: ${order._id}, Product: ${order.productName}`);
        });
      }
    }
    
    await mongoose.disconnect();
    console.log('\nDatabase disconnected');
  } catch (error) {
    console.error('Error:', error);
  }
}

checkOrderData();
