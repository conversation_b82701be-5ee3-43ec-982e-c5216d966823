require('dotenv').config();
const axios = require('axios');

const BASE_URL = 'http://localhost:3000';

async function testRealChatOrder() {
  try {
    console.log('🧪 اختبار إنشاء طلب حقيقي من المحادثة');
    console.log('=' .repeat(50));
    
    // Login with existing user
    const loginResult = await axios.post(`${BASE_URL}/api/auth/login`, {
      username: 'el<PERSON><PERSON><PERSON>',
      password: 'ym1792002'
    });
    
    const authToken = loginResult.data.token;
    console.log('✅ تم تسجيل الدخول بنجاح');
    
    // Get available products first
    const productsResult = await axios.get(`${BASE_URL}/api/products`, {
      headers: { 'x-auth-token': authToken }
    });
    
    console.log(`\n📦 المنتجات المتوفرة: ${productsResult.data.length}`);
    if (productsResult.data.length > 0) {
      productsResult.data.forEach((product, index) => {
        console.log(`   ${index + 1}. ${product.name} - ${product.price}`);
      });
    }
    
    // Test complete order conversation
    console.log('\n💬 بدء محادثة طلب كاملة...');
    
    const messages = [
      'أريد أطلب ايفون 16 بروو ماكس، اسمي علي أحمد، رقمي 07901234567، عنواني بغداد الكرادة شارع فلسطين'
    ];
    
    for (let i = 0; i < messages.length; i++) {
      const message = messages[i];
      console.log(`\n📝 رسالة ${i + 1}: "${message}"`);
      
      const chatResult = await axios.post(`${BASE_URL}/api/messages/chat`, {
        message: message,
        platform: 'test'
      }, {
        headers: { 'x-auth-token': authToken }
      });
      
      console.log(`🤖 رد البوت: ${chatResult.data.message.substring(0, 100)}...`);
      
      if (chatResult.data.orderCreated) {
        console.log('🎉 تم إنشاء طلب جديد!');
        
        // Get the latest order
        const ordersResult = await axios.get(`${BASE_URL}/api/orders`, {
          headers: { 'x-auth-token': authToken }
        });
        
        if (ordersResult.data.length > 0) {
          const latestOrder = ordersResult.data[0];
          console.log('\n📦 تفاصيل الطلب الجديد:');
          console.log(`   ID: ${latestOrder._id}`);
          console.log(`   المنتج: ${latestOrder.productName || 'غير محدد'}`);
          console.log(`   العميل: ${latestOrder.customerName || 'غير محدد'}`);
          console.log(`   الهاتف: ${latestOrder.customerPhone || 'غير محدد'}`);
          console.log(`   العنوان: ${latestOrder.customerAddress || 'غير محدد'}`);
          console.log(`   المصدر: ${latestOrder.source || 'غير محدد'}`);
          
          if (latestOrder.customerAddress && latestOrder.customerAddress.trim()) {
            console.log('\n✅ العنوان تم استخراجه بنجاح!');
            console.log(`   العنوان المستخرج: "${latestOrder.customerAddress}"`);
          } else {
            console.log('\n❌ العنوان لم يتم استخراجه');
          }
        }
        break;
      } else if (chatResult.data.orderStatus === 'PENDING') {
        console.log('⏳ الطلب في انتظار معلومات إضافية');
      }
      
      // Wait between messages
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
    
    // Test with available product
    if (productsResult.data.length > 0) {
      const availableProduct = productsResult.data[0];
      console.log(`\n💬 اختبار مع منتج متوفر: ${availableProduct.name}`);
      
      const productMessage = `أريد أطلب ${availableProduct.name}، اسمي سارة محمد، رقمي 07801234567، عنواني النجف الكوفة`;
      console.log(`📝 رسالة: "${productMessage}"`);
      
      const chatResult = await axios.post(`${BASE_URL}/api/messages/chat`, {
        message: productMessage,
        platform: 'test'
      }, {
        headers: { 'x-auth-token': authToken }
      });
      
      console.log(`🤖 رد البوت: ${chatResult.data.message.substring(0, 100)}...`);
      
      if (chatResult.data.orderCreated) {
        console.log('🎉 تم إنشاء طلب مع منتج متوفر!');
        
        // Get the latest order
        const ordersResult = await axios.get(`${BASE_URL}/api/orders`, {
          headers: { 'x-auth-token': authToken }
        });
        
        if (ordersResult.data.length > 0) {
          const latestOrder = ordersResult.data[0];
          console.log('\n📦 تفاصيل الطلب الجديد:');
          console.log(`   المنتج: ${latestOrder.productName || 'غير محدد'}`);
          console.log(`   العميل: ${latestOrder.customerName || 'غير محدد'}`);
          console.log(`   الهاتف: ${latestOrder.customerPhone || 'غير محدد'}`);
          console.log(`   العنوان: ${latestOrder.customerAddress || 'غير محدد'}`);
          
          if (latestOrder.customerAddress && latestOrder.customerAddress.trim()) {
            console.log('\n✅ العنوان تم استخراجه بنجاح من المنتج المتوفر!');
            console.log(`   العنوان: "${latestOrder.customerAddress}"`);
          } else {
            console.log('\n❌ العنوان لم يتم استخراجه من المنتج المتوفر');
          }
        }
      }
    }
    
    // Check all recent orders
    console.log('\n📊 فحص آخر الطلبات...');
    const allOrdersResult = await axios.get(`${BASE_URL}/api/orders`, {
      headers: { 'x-auth-token': authToken }
    });
    
    const recentOrders = allOrdersResult.data.slice(0, 5);
    console.log(`\nآخر ${recentOrders.length} طلبات:`);
    
    let ordersWithAddress = 0;
    recentOrders.forEach((order, index) => {
      console.log(`\n${index + 1}. ${order.productName || 'غير محدد'}`);
      console.log(`   العميل: ${order.customerName || 'غير محدد'}`);
      console.log(`   الهاتف: ${order.customerPhone || 'غير محدد'}`);
      console.log(`   العنوان: ${order.customerAddress || 'غير محدد'}`);
      console.log(`   المصدر: ${order.source || 'غير محدد'}`);
      
      if (order.customerAddress && order.customerAddress.trim()) {
        console.log(`   ✅ العنوان موجود`);
        ordersWithAddress++;
      } else {
        console.log(`   ❌ العنوان مفقود`);
      }
    });
    
    console.log('\n' + '=' .repeat(50));
    console.log('📈 الإحصائيات النهائية:');
    console.log(`   طلبات مع عنوان: ${ordersWithAddress}/${recentOrders.length}`);
    console.log(`   معدل النجاح: ${((ordersWithAddress / recentOrders.length) * 100).toFixed(1)}%`);
    
    if (ordersWithAddress > 0) {
      console.log('\n🎉 استخراج العنوان يعمل بنجاح!');
    } else {
      console.log('\n⚠️ يحتاج مراجعة استخراج العنوان');
    }
    
    console.log('\n💡 للتحقق من النتائج في الواجهة:');
    console.log('1. افتح http://localhost:3000');
    console.log('2. سجل الدخول وانتقل لصفحة الطلبات');
    console.log('3. تحقق من عمود "معلومات العميل"');
    console.log('4. يجب أن ترى العناوين تظهر بوضوح');
    
  } catch (error) {
    console.error('\n❌ خطأ في الاختبار:', error.message);
    if (error.response) {
      console.error('تفاصيل الخطأ:', error.response.data);
    }
  }
}

testRealChatOrder();
