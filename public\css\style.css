/* General Styles */
body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background-color: #f8f9fa;
}

.page {
  display: none;
}

.card {
  border-radius: 10px;
  border: none;
  overflow: hidden;
}

.card-header {
  border-bottom: none;
}

.btn {
  border-radius: 5px;
  padding: 8px 16px;
}

.btn-primary {
  background-color: #4361ee;
  border-color: #4361ee;
}

.btn-primary:hover {
  background-color: #3a56d4;
  border-color: #3a56d4;
}

.navbar {
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.navbar-brand {
  font-weight: bold;
}

/* Chat Styles */
.chat-container {
  height: 400px;
  overflow-y: auto;
  padding: 15px;
  display: flex;
  flex-direction: column;
  background-color: #f9f9f9;
  border-radius: 10px;
}

.user-message, .bot-message, .system-message {
  max-width: 80%;
  margin-bottom: 15px;
  position: relative;
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.message-content {
  padding: 12px 15px;
  border-radius: 15px;
  line-height: 1.5;
  word-wrap: break-word;
}

.user-message {
  align-self: flex-end;
}

.user-message .message-content {
  background-color: #4361ee;
  color: white;
  border-bottom-right-radius: 5px;
}

.bot-message {
  align-self: flex-start;
}

.bot-message .message-content {
  background-color: #f1f1f1;
  color: #333;
  border-bottom-left-radius: 5px;
}

.system-message {
  align-self: center;
  max-width: 90%;
}

.system-message .message-content {
  background-color: #f8f9fa;
  color: #6c757d;
  border-radius: 10px;
  font-style: italic;
  text-align: center;
}

.message-time {
  font-size: 0.7rem;
  color: rgba(0, 0, 0, 0.5);
  margin-top: 4px;
  display: block;
  text-align: right;
}

.user-message .message-time {
  color: rgba(0, 0, 0, 0.5);
}

.bot-message .message-time {
  text-align: left;
}

.message-indicator {
  width: 10px;
  height: 10px;
  position: absolute;
  bottom: 5px;
}

.user-message .message-indicator {
  right: -5px;
  border-right: 10px solid #4361ee;
  border-bottom: 10px solid transparent;
}

.bot-message .message-indicator {
  left: -5px;
  border-left: 10px solid #f1f1f1;
  border-bottom: 10px solid transparent;
}

/* Typing indicator */
.typing-indicator {
  display: flex;
  align-items: center;
}

.typing-indicator span {
  margin-right: 2px;
}

.typing-indicator .dot {
  animation: typingAnimation 1.4s infinite;
  display: inline-block;
}

.typing-indicator .dot:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-indicator .dot:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes typingAnimation {
  0% { opacity: 0.3; }
  50% { opacity: 1; }
  100% { opacity: 0.3; }
}

/* Dashboard Styles */
.card i.fa-3x {
  margin-bottom: 15px;
}

/* Products Table */
.table {
  margin-bottom: 0;
}

.table th {
  font-weight: 600;
  color: #495057;
}

.action-buttons .btn {
  padding: 4px 8px;
  font-size: 0.8rem;
}

/* Customer Info Styles */
.customer-info {
  font-size: 0.9rem;
  line-height: 1.6;
}

.customer-info strong {
  color: #495057;
  font-weight: 600;
}

.customer-info br + strong {
  margin-top: 4px;
  display: inline-block;
}

/* Orders Table Enhancements */
.orders-table td {
  vertical-align: middle;
  padding: 12px 8px;
}

.orders-table .customer-info-cell {
  max-width: 200px;
  word-wrap: break-word;
  white-space: normal;
}

.orders-table .badge {
  font-size: 0.75rem;
  padding: 4px 8px;
}

/* Responsive table improvements */
@media (max-width: 992px) {
  .orders-table .customer-info-cell {
    max-width: 150px;
    font-size: 0.8rem;
  }

  .action-buttons .btn {
    padding: 2px 6px;
    font-size: 0.7rem;
  }
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .user-message, .bot-message {
    max-width: 90%;
  }
}

/* Loading Spinner */
.spinner-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100px;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-left-color: #4361ee;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* RTL Specific Adjustments */
.dropdown-menu {
  text-align: right;
}

.modal-header .btn-close {
  margin: -0.5rem auto -0.5rem -0.5rem;
}

/* Admin Panel Styles */
.admin-card {
  transition: transform 0.3s;
}

.admin-card:hover {
  transform: translateY(-5px);
}

.admin-card .card-body {
  padding: 2rem;
}

.admin-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  color: #4361ee;
}

/* OAuth Success Page */
.success-container {
  text-align: center;
  padding: 50px 20px;
}

.success-icon {
  font-size: 5rem;
  color: #28a745;
  margin-bottom: 20px;
}

.error-icon {
  font-size: 5rem;
  color: #dc3545;
  margin-bottom: 20px;
}
