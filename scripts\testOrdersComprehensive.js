require('dotenv').config();
const axios = require('axios');
const database = require('../config/database');

const BASE_URL = 'http://localhost:3000';
let authToken = null;

// Test data
const testUser = {
  username: 'comprehensive_' + Date.now(),
  password: 'testpassword123',
  email: '<EMAIL>',
  name: 'Comprehensive Test User'
};

async function makeRequest(method, endpoint, data = null, useAuth = false) {
  try {
    const config = {
      method,
      url: `${BASE_URL}${endpoint}`,
      headers: {}
    };

    if (useAuth && authToken) {
      config.headers['x-auth-token'] = authToken;
    }

    if (data) {
      config.data = data;
      config.headers['Content-Type'] = 'application/json';
    }

    const response = await axios(config);
    return { success: true, data: response.data, status: response.status };
  } catch (error) {
    return {
      success: false,
      error: error.response ? error.response.data : error.message,
      status: error.response ? error.response.status : 500
    };
  }
}

async function setupTestUser() {
  console.log('\n--- إعداد مستخدم الاختبار ---');
  
  const result = await makeRequest('POST', '/api/auth/register', testUser);
  
  if (result.success) {
    console.log('✅ تم إنشاء مستخدم الاختبار بنجاح');
    authToken = result.data.token;
    return true;
  } else {
    console.log('❌ فشل في إنشاء مستخدم الاختبار:', result.error);
    return false;
  }
}

async function testManualOrderCreation() {
  console.log('\n--- اختبار إنشاء الطلبات يدوياً ---');
  
  const orders = [
    {
      productName: 'لابتوب ديل',
      quantity: 1,
      customerName: 'سارة أحمد',
      customerPhone: '07701234567',
      customerAddress: 'النجف، العراق',
      totalAmount: '800',
      notes: 'يفضل التسليم صباحاً',
      source: 'manual'
    },
    {
      productName: 'ماوس لوجيتك',
      quantity: 3,
      customerName: 'محمد علي',
      customerPhone: '07801234567',
      customerAddress: 'كربلاء، العراق',
      totalAmount: '150',
      notes: 'طلب عاجل',
      source: 'api'
    }
  ];
  
  const createdOrders = [];
  
  for (const order of orders) {
    console.log(`\n🔸 إنشاء طلب: ${order.productName}`);
    
    const result = await makeRequest('POST', '/api/orders', order, true);
    
    if (result.success) {
      createdOrders.push(result.data);
      console.log('✅ تم إنشاء الطلب بنجاح');
      console.log(`   ID: ${result.data._id}`);
      console.log(`   المنتج: ${result.data.productName}`);
      console.log(`   العميل: ${result.data.customerName}`);
      console.log(`   الكمية: ${result.data.quantity}`);
      console.log(`   المصدر: ${result.data.source}`);
    } else {
      console.log('❌ فشل في إنشاء الطلب:', result.error);
    }
  }
  
  return createdOrders;
}

async function testOrderValidation() {
  console.log('\n--- اختبار التحقق من صحة البيانات ---');
  
  // Test missing required fields
  console.log('\n🔸 اختبار الحقول المطلوبة المفقودة');
  
  let result = await makeRequest('POST', '/api/orders', {
    quantity: 1,
    customerName: 'اختبار'
  }, true);
  
  if (!result.success && result.status === 400) {
    console.log('✅ تم رفض الطلب بسبب عدم وجود اسم المنتج');
  } else {
    console.log('❌ لم يتم التحقق من الحقول المطلوبة بشكل صحيح');
  }
  
  // Test invalid status
  console.log('\n🔸 اختبار حالة غير صحيحة');
  
  result = await makeRequest('POST', '/api/orders', {
    productName: 'منتج تجريبي',
    status: 'invalid_status'
  }, true);
  
  if (result.success) {
    console.log('✅ تم إنشاء الطلب مع تجاهل الحالة غير الصحيحة');
  } else {
    console.log('❌ فشل في إنشاء الطلب:', result.error);
  }
}

async function testOrderUpdates() {
  console.log('\n--- اختبار تحديث الطلبات ---');
  
  // Create a test order first
  const orderResult = await makeRequest('POST', '/api/orders', {
    productName: 'منتج للتحديث',
    quantity: 1,
    customerName: 'عميل التحديث',
    totalAmount: '100'
  }, true);
  
  if (!orderResult.success) {
    console.log('❌ فشل في إنشاء طلب للاختبار');
    return;
  }
  
  const orderId = orderResult.data._id;
  console.log(`\n🔸 تحديث الطلب: ${orderId}`);
  
  // Test updating multiple fields
  let result = await makeRequest('PUT', `/api/orders/${orderId}`, {
    quantity: 5,
    customerPhone: '07901111111',
    notes: 'تم التحديث بنجاح',
    status: 'processing'
  }, true);
  
  if (result.success) {
    console.log('✅ تم تحديث الطلب بنجاح');
    console.log(`   الكمية الجديدة: ${result.data.quantity}`);
    console.log(`   الهاتف الجديد: ${result.data.customerPhone}`);
    console.log(`   الحالة الجديدة: ${result.data.status}`);
  } else {
    console.log('❌ فشل في تحديث الطلب:', result.error);
  }
  
  // Test invalid status update
  console.log('\n🔸 اختبار تحديث بحالة غير صحيحة');
  
  result = await makeRequest('PUT', `/api/orders/${orderId}`, {
    status: 'invalid_status'
  }, true);
  
  if (!result.success && result.status === 400) {
    console.log('✅ تم رفض التحديث بسبب الحالة غير الصحيحة');
  } else {
    console.log('❌ لم يتم التحقق من الحالة بشكل صحيح');
  }
  
  return orderId;
}

async function testOrderDeletion(orderId) {
  console.log('\n--- اختبار حذف الطلبات ---');
  
  if (!orderId) {
    console.log('لا يوجد طلب للحذف');
    return;
  }
  
  console.log(`\n🔸 حذف الطلب: ${orderId}`);
  
  const result = await makeRequest('DELETE', `/api/orders/${orderId}`, null, true);
  
  if (result.success) {
    console.log('✅ تم حذف الطلب بنجاح');
  } else {
    console.log('❌ فشل في حذف الطلب:', result.error);
  }
  
  // Test deleting non-existent order
  console.log('\n🔸 اختبار حذف طلب غير موجود');
  
  const deleteResult = await makeRequest('DELETE', '/api/orders/non-existent-id', null, true);
  
  if (!deleteResult.success && deleteResult.status === 404) {
    console.log('✅ تم رفض حذف الطلب غير الموجود بشكل صحيح');
  } else {
    console.log('❌ لم يتم التعامل مع الطلب غير الموجود بشكل صحيح');
  }
}

async function testOrdersRetrieval() {
  console.log('\n--- اختبار استرجاع الطلبات ---');
  
  const result = await makeRequest('GET', '/api/orders', null, true);
  
  if (result.success) {
    console.log(`✅ تم استرجاع ${result.data.length} طلب`);
    
    if (result.data.length > 0) {
      console.log('\n📋 تفاصيل الطلبات:');
      result.data.forEach((order, index) => {
        console.log(`\n   الطلب ${index + 1}:`);
        console.log(`     المنتج: ${order.productName || 'غير محدد'}`);
        console.log(`     العميل: ${order.customerName || 'غير محدد'}`);
        console.log(`     الحالة: ${order.status || 'غير محدد'}`);
        console.log(`     المصدر: ${order.source || 'غير محدد'}`);
      });
    }
    
    return result.data;
  } else {
    console.log('❌ فشل في استرجاع الطلبات:', result.error);
    return [];
  }
}

async function runComprehensiveTest() {
  try {
    console.log('🚀 بدء الاختبار الشامل لنظام الطلبات...');
    
    // Connect to database
    await database.connect();
    console.log('✅ تم الاتصال بقاعدة البيانات');
    
    // Setup test user
    const userSetup = await setupTestUser();
    if (!userSetup) {
      throw new Error('Failed to setup test user');
    }
    
    // Test manual order creation
    await testManualOrderCreation();
    
    // Test order validation
    await testOrderValidation();
    
    // Test order updates
    const testOrderId = await testOrderUpdates();
    
    // Test orders retrieval
    await testOrdersRetrieval();
    
    // Test order deletion
    await testOrderDeletion(testOrderId);
    
    // Final retrieval to confirm deletion
    await testOrdersRetrieval();
    
    console.log('\n🎉 تم إنجاز جميع الاختبارات الشاملة بنجاح!');
    
    console.log('\n📊 ملخص النتائج:');
    console.log('   ✅ إنشاء الطلبات يدوياً');
    console.log('   ✅ التحقق من صحة البيانات');
    console.log('   ✅ تحديث الطلبات');
    console.log('   ✅ استرجاع الطلبات');
    console.log('   ✅ حذف الطلبات');
    console.log('   ✅ معالجة الأخطاء');
    
  } catch (error) {
    console.error('\n❌ فشل في تشغيل الاختبار الشامل:', error.message);
  } finally {
    // Disconnect from database
    await database.disconnect();
    console.log('✅ تم قطع الاتصال بقاعدة البيانات');
    process.exit(0);
  }
}

// Run the comprehensive test
runComprehensiveTest();
