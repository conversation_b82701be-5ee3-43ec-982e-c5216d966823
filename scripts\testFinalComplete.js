require('dotenv').config();
const axios = require('axios');
const database = require('../config/database');

const BASE_URL = 'http://localhost:3000';
let authToken = null;

// Test data
const testUser = {
  username: 'finaltest_' + Date.now(),
  password: 'testpassword123',
  email: '<EMAIL>',
  name: 'Final Test User'
};

async function makeRequest(method, endpoint, data = null, useAuth = false) {
  try {
    const config = {
      method,
      url: `${BASE_URL}${endpoint}`,
      headers: {}
    };

    if (useAuth && authToken) {
      config.headers['x-auth-token'] = authToken;
    }

    if (data) {
      config.data = data;
      config.headers['Content-Type'] = 'application/json';
    }

    const response = await axios(config);
    return { success: true, data: response.data, status: response.status };
  } catch (error) {
    return {
      success: false,
      error: error.response ? error.response.data : error.message,
      status: error.response ? error.response.status : 500
    };
  }
}

async function setupTestEnvironment() {
  console.log('\n🔧 إعداد البيئة التجريبية...');
  
  // Register user
  const userResult = await makeRequest('POST', '/api/auth/register', testUser);
  if (!userResult.success) {
    throw new Error('Failed to create test user');
  }
  
  authToken = userResult.data.token;
  console.log('✅ تم إنشاء مستخدم الاختبار');
  
  // Add products
  const products = [
    { name: 'آيفون 15 برو', price: '1200 دولار', description: 'هاتف ذكي متطور' },
    { name: 'آيربودز برو', price: '250 دولار', description: 'سماعات لاسلكية' },
    { name: 'آبل ووتش', price: '400 دولار', description: 'ساعة ذكية' }
  ];
  
  for (const product of products) {
    await makeRequest('POST', '/api/products', product, true);
  }
  console.log('✅ تم إضافة المنتجات التجريبية');
  
  // Setup store info
  const storeInfo = {
    name: 'متجر التكنولوجيا النهائي',
    address: 'شارع الحبيبية، بغداد، العراق',
    description: 'متجر متخصص في بيع أحدث المنتجات التكنولوجية'
  };
  
  await makeRequest('POST', '/api/store/info', storeInfo, true);
  console.log('✅ تم إعداد معلومات المتجر');
}

async function testCompleteOrderFlow() {
  console.log('\n🛒 اختبار تدفق الطلبات الكامل...');
  
  let testsPassed = 0;
  let totalTests = 0;
  
  // Test 1: Chat order creation (incomplete)
  totalTests++;
  console.log('\n📱 اختبار 1: طلب من المحادثة (ناقص)');
  let result = await makeRequest('POST', '/api/messages/chat', {
    message: 'أريد شراء آيفون 15 برو',
    platform: 'test'
  }, true);
  
  if (result.success && result.data.orderStatus === 'PENDING') {
    console.log('✅ تم التعرف على الطلب وطلب معلومات العميل');
    testsPassed++;
  } else {
    console.log('❌ فشل في التعرف على الطلب');
  }
  
  // Test 2: Complete chat order
  totalTests++;
  console.log('\n📱 اختبار 2: إكمال الطلب من المحادثة');
  result = await makeRequest('POST', '/api/messages/chat', {
    message: 'اسمي أحمد محمد، رقمي 07901234567، عنواني بغداد الكرادة',
    platform: 'test'
  }, true);
  
  if (result.success && result.data.orderStatus === 'CONFIRMED' && result.data.orderCreated) {
    console.log('✅ تم إنشاء الطلب بنجاح من المحادثة');
    testsPassed++;
  } else {
    console.log('❌ فشل في إنشاء الطلب من المحادثة');
  }
  
  // Test 3: One-message complete order
  totalTests++;
  console.log('\n📱 اختبار 3: طلب كامل في رسالة واحدة');
  result = await makeRequest('POST', '/api/messages/chat', {
    message: 'أريد شراء آيربودز برو، اسمي فاطمة علي، رقمي 07801234567، عنواني البصرة',
    platform: 'test'
  }, true);
  
  if (result.success && result.data.orderStatus === 'CONFIRMED' && result.data.orderCreated) {
    console.log('✅ تم إنشاء الطلب الكامل في رسالة واحدة');
    testsPassed++;
  } else {
    console.log('❌ فشل في إنشاء الطلب الكامل');
  }
  
  // Test 4: Manual order creation
  totalTests++;
  console.log('\n🖱️ اختبار 4: إنشاء طلب يدوي');
  result = await makeRequest('POST', '/api/orders', {
    productName: 'آبل ووتش',
    quantity: 1,
    customerName: 'سارة أحمد',
    customerPhone: '07701234567',
    customerAddress: 'النجف، العراق',
    totalAmount: '400',
    source: 'manual'
  }, true);
  
  if (result.success && result.data.productName === 'آبل ووتش') {
    console.log('✅ تم إنشاء الطلب اليدوي بنجاح');
    testsPassed++;
  } else {
    console.log('❌ فشل في إنشاء الطلب اليدوي');
  }
  
  // Test 5: Get all orders
  totalTests++;
  console.log('\n📋 اختبار 5: استرجاع جميع الطلبات');
  result = await makeRequest('GET', '/api/orders', null, true);
  
  if (result.success && result.data.length >= 3) {
    console.log(`✅ تم استرجاع ${result.data.length} طلب بنجاح`);
    testsPassed++;
    
    // Display order details
    result.data.forEach((order, index) => {
      console.log(`   الطلب ${index + 1}: ${order.productName} - ${order.customerName} - ${order.source}`);
    });
  } else {
    console.log('❌ فشل في استرجاع الطلبات');
  }
  
  // Test 6: Update order
  if (result.success && result.data.length > 0) {
    totalTests++;
    console.log('\n✏️ اختبار 6: تحديث الطلب');
    const orderId = result.data[0]._id;
    
    const updateResult = await makeRequest('PUT', `/api/orders/${orderId}`, {
      status: 'processing',
      notes: 'تم التحديث في الاختبار النهائي'
    }, true);
    
    if (updateResult.success && updateResult.data.status === 'processing') {
      console.log('✅ تم تحديث الطلب بنجاح');
      testsPassed++;
    } else {
      console.log('❌ فشل في تحديث الطلب');
    }
  }
  
  return { testsPassed, totalTests };
}

async function testSystemIntegration() {
  console.log('\n🔗 اختبار تكامل النظام...');
  
  let testsPassed = 0;
  let totalTests = 0;
  
  // Test 1: Message statistics
  totalTests++;
  console.log('\n📊 اختبار 1: إحصائيات الرسائل');
  let result = await makeRequest('GET', '/api/messages/stats', null, true);
  
  if (result.success && result.data.totalMessages > 0) {
    console.log(`✅ تم الحصول على إحصائيات الرسائل: ${result.data.totalMessages} رسالة`);
    testsPassed++;
  } else {
    console.log('❌ فشل في الحصول على إحصائيات الرسائل');
  }
  
  // Test 2: Product management
  totalTests++;
  console.log('\n📦 اختبار 2: إدارة المنتجات');
  result = await makeRequest('GET', '/api/products', null, true);
  
  if (result.success && result.data.length >= 3) {
    console.log(`✅ تم استرجاع ${result.data.length} منتج`);
    testsPassed++;
  } else {
    console.log('❌ فشل في استرجاع المنتجات');
  }
  
  // Test 3: Store information
  totalTests++;
  console.log('\n🏪 اختبار 3: معلومات المتجر');
  result = await makeRequest('GET', '/api/store/info', null, true);
  
  if (result.success && result.data.name) {
    console.log(`✅ تم استرجاع معلومات المتجر: ${result.data.name}`);
    testsPassed++;
  } else {
    console.log('❌ فشل في استرجاع معلومات المتجر');
  }
  
  // Test 4: User authentication
  totalTests++;
  console.log('\n👤 اختبار 4: المصادقة');
  result = await makeRequest('GET', '/api/auth/me', null, true);
  
  if (result.success && result.data.username) {
    console.log(`✅ تم التحقق من المصادقة: ${result.data.username}`);
    testsPassed++;
  } else {
    console.log('❌ فشل في التحقق من المصادقة');
  }
  
  return { testsPassed, totalTests };
}

async function runFinalCompleteTest() {
  try {
    console.log('🚀 بدء الاختبار النهائي الشامل لتطبيق AutoReply...');
    console.log('=' .repeat(60));
    
    // Connect to database
    await database.connect();
    console.log('✅ تم الاتصال بقاعدة البيانات');
    
    // Setup test environment
    await setupTestEnvironment();
    
    // Test complete order flow
    const orderResults = await testCompleteOrderFlow();
    
    // Test system integration
    const integrationResults = await testSystemIntegration();
    
    // Calculate final results
    const totalPassed = orderResults.testsPassed + integrationResults.testsPassed;
    const totalTests = orderResults.totalTests + integrationResults.totalTests;
    const successRate = ((totalPassed / totalTests) * 100).toFixed(1);
    
    console.log('\n' + '=' .repeat(60));
    console.log('📊 النتائج النهائية:');
    console.log('=' .repeat(60));
    
    console.log(`\n🛒 اختبارات الطلبات:`);
    console.log(`   نجح: ${orderResults.testsPassed}/${orderResults.totalTests}`);
    
    console.log(`\n🔗 اختبارات التكامل:`);
    console.log(`   نجح: ${integrationResults.testsPassed}/${integrationResults.totalTests}`);
    
    console.log(`\n📈 النتيجة الإجمالية:`);
    console.log(`   نجح: ${totalPassed}/${totalTests} (${successRate}%)`);
    
    if (successRate >= 90) {
      console.log('\n🎉 تم إنجاز جميع الاختبارات بنجاح!');
      console.log('✅ تطبيق AutoReply جاهز للاستخدام الإنتاجي');
    } else {
      console.log('\n⚠️ بعض الاختبارات فشلت، يحتاج مراجعة');
    }
    
    console.log('\n🔧 الميزات المختبرة:');
    console.log('   ✅ إنشاء الطلبات من المحادثة');
    console.log('   ✅ تحليل معلومات العميل تلقائياً');
    console.log('   ✅ إدارة الطلبات (CRUD)');
    console.log('   ✅ تكامل قاعدة البيانات MongoDB');
    console.log('   ✅ API شامل للطلبات');
    console.log('   ✅ معالجة الأخطاء والتحقق من البيانات');
    console.log('   ✅ إحصائيات ومراقبة النظام');
    
  } catch (error) {
    console.error('\n❌ فشل في تشغيل الاختبار النهائي:', error.message);
  } finally {
    // Disconnect from database
    await database.disconnect();
    console.log('\n✅ تم قطع الاتصال بقاعدة البيانات');
    console.log('\n🏁 انتهى الاختبار النهائي');
    process.exit(0);
  }
}

// Run the final complete test
runFinalCompleteTest();
