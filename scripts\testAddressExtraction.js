require('dotenv').config();
const axios = require('axios');

const BASE_URL = 'http://localhost:3000';

// Use existing user credentials
const existingUser = {
  username: 'el<PERSON><PERSON><PERSON>',
  password: 'ym1792002'
};

async function makeRequest(method, endpoint, data = null, useAuth = false, token = null) {
  try {
    const config = {
      method,
      url: `${BASE_URL}${endpoint}`,
      headers: {}
    };

    if (useAuth && token) {
      config.headers['x-auth-token'] = token;
    }

    if (data) {
      config.data = data;
      config.headers['Content-Type'] = 'application/json';
    }

    const response = await axios(config);
    return { success: true, data: response.data, status: response.status };
  } catch (error) {
    return {
      success: false,
      error: error.response ? error.response.data : error.message,
      status: error.response ? error.response.status : 500
    };
  }
}

async function testAddressExtraction() {
  try {
    console.log('🧪 اختبار استخراج العنوان من المحادثة');
    console.log('=' .repeat(60));
    
    // Login
    console.log('🔐 تسجيل الدخول...');
    const loginResult = await makeRequest('POST', '/api/auth/login', existingUser);
    
    if (!loginResult.success) {
      console.log('❌ فشل في تسجيل الدخول:', loginResult.error);
      return;
    }
    
    const authToken = loginResult.data.token;
    console.log('✅ تم تسجيل الدخول بنجاح');
    
    // Test different address formats in chat
    const testScenarios = [
      {
        name: 'سيناريو 1: عنوان مع كلمة "عنواني"',
        messages: [
          'أريد أطلب آيباد',
          'اسمي أحمد محمد',
          'رقمي 07901234567',
          'عنواني بغداد الكرادة شارع أبو نواس'
        ]
      },
      {
        name: 'سيناريو 2: عنوان بدون كلمة مفتاحية',
        messages: [
          'أريد أطلب آيباد',
          'اسمي فاطمة علي',
          'رقمي 07801234567',
          'البصرة المعقل'
        ]
      },
      {
        name: 'سيناريو 3: جميع المعلومات في رسالة واحدة',
        messages: [
          'أريد أطلب آيباد، اسمي سارة أحمد، رقمي 07701234567، عنواني النجف الكوفة'
        ]
      },
      {
        name: 'سيناريو 4: عنوان مع مدينة أخرى',
        messages: [
          'أريد أطلب آيباد',
          'اسمي محمد حسن',
          'رقمي 07601234567',
          'أربيل عنكاوا'
        ]
      },
      {
        name: 'سيناريو 5: عنوان مفصل',
        messages: [
          'أريد أطلب آيباد',
          'اسمي علي أحمد',
          'رقمي 07501234567',
          'موصل حي نابلس شارع الجامعة'
        ]
      }
    ];
    
    for (let i = 0; i < testScenarios.length; i++) {
      const scenario = testScenarios[i];
      console.log(`\n📋 ${scenario.name}`);
      console.log('-' .repeat(40));
      
      // Send messages in sequence
      for (let j = 0; j < scenario.messages.length; j++) {
        const message = scenario.messages[j];
        console.log(`\n💬 رسالة ${j + 1}: "${message}"`);
        
        const chatResult = await makeRequest('POST', '/api/messages/chat', {
          message: message,
          platform: 'test'
        }, true, authToken);
        
        if (chatResult.success) {
          console.log(`✅ رد البوت: ${chatResult.data.message.substring(0, 100)}...`);
          
          if (chatResult.data.orderCreated) {
            console.log('🎉 تم إنشاء طلب!');
            
            // Get the latest order to check address
            const ordersResult = await makeRequest('GET', '/api/orders', null, true, authToken);
            
            if (ordersResult.success && ordersResult.data.length > 0) {
              const latestOrder = ordersResult.data[0];
              console.log('📦 تفاصيل الطلب الجديد:');
              console.log(`   المنتج: ${latestOrder.productName || 'غير محدد'}`);
              console.log(`   العميل: ${latestOrder.customerName || 'غير محدد'}`);
              console.log(`   الهاتف: ${latestOrder.customerPhone || 'غير محدد'}`);
              console.log(`   العنوان: ${latestOrder.customerAddress || 'غير محدد'}`);
              
              if (latestOrder.customerAddress && latestOrder.customerAddress.trim()) {
                console.log('✅ العنوان تم استخراجه بنجاح');
              } else {
                console.log('❌ العنوان لم يتم استخراجه');
              }
            }
            break; // Exit message loop if order was created
          } else if (chatResult.data.orderStatus === 'PENDING') {
            console.log('⏳ الطلب في انتظار معلومات إضافية');
          }
        } else {
          console.log(`❌ خطأ في الرسالة: ${chatResult.error}`);
        }
        
        // Wait a bit between messages
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }
    
    // Test the parsing function directly
    console.log('\n🔧 اختبار دالة استخراج العنوان مباشرة:');
    console.log('=' .repeat(60));
    
    const testTexts = [
      'يوسف محمد، 07518838204، موصل حي نابلس',
      'أحمد علي، 07901234567، عنواني بغداد الكرادة',
      'فاطمة سالم، 07801234567، البصرة المعقل',
      'سارة أحمد، 07701234567، النجف الكوفة شارع الإمام علي',
      'محمد حسن، 07601234567، أربيل عنكاوا مجمع الأندلس'
    ];
    
    // Since we can't directly call the parseCustomerInfo function from here,
    // we'll test it through the chat API and check the results
    console.log('\n📝 نتائج الاختبار:');
    
    // Get all recent orders to see the results
    const finalOrdersResult = await makeRequest('GET', '/api/orders', null, true, authToken);
    
    if (finalOrdersResult.success) {
      const recentOrders = finalOrdersResult.data.slice(0, 10);
      
      console.log(`\n📊 آخر ${recentOrders.length} طلبات:`);
      
      let ordersWithAddress = 0;
      let ordersWithoutAddress = 0;
      
      recentOrders.forEach((order, index) => {
        console.log(`\n${index + 1}. ${order.productName || 'غير محدد'}`);
        console.log(`   العميل: ${order.customerName || 'غير محدد'}`);
        console.log(`   الهاتف: ${order.customerPhone || 'غير محدد'}`);
        console.log(`   العنوان: ${order.customerAddress || 'غير محدد'}`);
        console.log(`   المصدر: ${order.source || 'غير محدد'}`);
        
        if (order.customerAddress && order.customerAddress.trim()) {
          console.log(`   ✅ العنوان موجود`);
          ordersWithAddress++;
        } else {
          console.log(`   ❌ العنوان مفقود`);
          ordersWithoutAddress++;
        }
      });
      
      console.log('\n📈 الإحصائيات:');
      console.log(`   طلبات مع عنوان: ${ordersWithAddress}/${recentOrders.length}`);
      console.log(`   طلبات بدون عنوان: ${ordersWithoutAddress}/${recentOrders.length}`);
      console.log(`   معدل النجاح: ${((ordersWithAddress / recentOrders.length) * 100).toFixed(1)}%`);
      
      if (ordersWithAddress > ordersWithoutAddress) {
        console.log('\n🎉 استخراج العنوان يعمل بشكل جيد!');
      } else {
        console.log('\n⚠️ يحتاج تحسين في استخراج العنوان');
      }
    }
    
    console.log('\n' + '=' .repeat(60));
    console.log('📋 ملخص الاختبار:');
    console.log('✅ تم تحسين دالة parseCustomerInfo');
    console.log('✅ إضافة أنماط جديدة لاستخراج العنوان');
    console.log('✅ دعم المدن العراقية المختلفة');
    console.log('✅ تحسين معالجة النصوص المختلطة');
    
    console.log('\n💡 للتحقق من النتائج:');
    console.log('1. افتح المتصفح على http://localhost:3000');
    console.log('2. سجل الدخول وانتقل لصفحة الطلبات');
    console.log('3. تحقق من عمود "معلومات العميل"');
    console.log('4. يجب أن ترى العناوين تظهر بوضوح');
    
  } catch (error) {
    console.error('\n❌ خطأ في الاختبار:', error.message);
  }
}

testAddressExtraction();
