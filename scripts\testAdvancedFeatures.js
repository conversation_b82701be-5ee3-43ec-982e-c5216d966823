require('dotenv').config();
const axios = require('axios');
const database = require('../config/database');

const BASE_URL = 'http://localhost:3001';
let authToken = null;
let testUserId = null;

// Test data
const testUser = {
  username: 'advancedtest_' + Date.now(),
  password: 'testpassword123',
  email: '<EMAIL>',
  name: 'Advanced Test User'
};

async function makeRequest(method, endpoint, data = null, useAuth = false) {
  try {
    const config = {
      method,
      url: `${BASE_URL}${endpoint}`,
      headers: {}
    };

    if (useAuth && authToken) {
      config.headers['x-auth-token'] = authToken;
    }

    if (data) {
      config.data = data;
      config.headers['Content-Type'] = 'application/json';
    }

    const response = await axios(config);
    return { success: true, data: response.data, status: response.status };
  } catch (error) {
    return {
      success: false,
      error: error.response ? error.response.data : error.message,
      status: error.response ? error.response.status : 500
    };
  }
}

async function setupTestUser() {
  console.log('\n--- إعداد مستخدم الاختبار ---');

  const result = await makeRequest('POST', '/api/auth/register', testUser);

  if (result.success) {
    console.log('✅ تم إنشاء مستخدم الاختبار بنجاح');
    authToken = result.data.token;
    testUserId = result.data.user.id;
    return true;
  } else {
    console.log('❌ فشل في إنشاء مستخدم الاختبار:', result.error);
    return false;
  }
}

async function testComplexProductScenario() {
  console.log('\n--- اختبار سيناريو معقد للمنتجات ---');

  const products = [
    { name: 'آيفون 15 برو', price: '1200 دولار', description: 'هاتف ذكي متطور' },
    { name: 'آيربودز برو', price: '250 دولار', description: 'سماعات لاسلكية' },
    { name: 'آبل ووتش', price: '400 دولار', description: 'ساعة ذكية' }
  ];

  const addedProducts = [];

  // إضافة عدة منتجات
  for (const product of products) {
    const result = await makeRequest('POST', '/api/products', product, true);
    if (result.success) {
      addedProducts.push(result.data);
      console.log(`✅ تم إضافة المنتج: ${product.name}`);
    } else {
      console.log(`❌ فشل في إضافة المنتج: ${product.name}`);
    }
  }

  // الحصول على جميع المنتجات
  const getResult = await makeRequest('GET', '/api/products', null, true);
  if (getResult.success) {
    console.log(`✅ تم الحصول على ${getResult.data.length} منتج`);
  }

  // تحديث منتج واحد
  if (addedProducts.length > 0) {
    const updateResult = await makeRequest('PUT', `/api/products/${addedProducts[0]._id}`, {
      price: '1100 دولار'
    }, true);

    if (updateResult.success) {
      console.log('✅ تم تحديث سعر المنتج بنجاح');
    }
  }

  return addedProducts;
}

async function testConversationFlow() {
  console.log('\n--- اختبار تدفق المحادثة ---');

  const messages = [
    'مرحبا، كيف حالك؟',
    'ما هي المنتجات المتوفرة لديكم؟',
    'أريد شراء آيفون 15 برو',
    'كم سعر الآيفون؟',
    'هل يمكنني طلب واحد؟'
  ];

  for (let i = 0; i < messages.length; i++) {
    const message = messages[i];
    console.log(`إرسال الرسالة ${i + 1}: ${message}`);

    const result = await makeRequest('POST', '/api/messages/chat', {
      message,
      platform: 'test'
    }, true);

    if (result.success) {
      console.log(`✅ تم الحصول على رد: ${result.data.message.substring(0, 50)}...`);

      // انتظار قصير بين الرسائل
      await new Promise(resolve => setTimeout(resolve, 1000));
    } else {
      console.log(`❌ فشل في إرسال الرسالة: ${result.error}`);
    }
  }

  // الحصول على تاريخ المحادثة
  const historyResult = await makeRequest('GET', '/api/messages', null, true);
  if (historyResult.success) {
    console.log(`✅ تم الحصول على ${historyResult.data.length} رسالة في التاريخ`);
  }
}

async function testOrderManagement() {
  console.log('\n--- اختبار إدارة الطلبات ---');

  const orders = [
    {
      productName: 'آيفون 15 برو',
      quantity: 1,
      customerName: 'أحمد محمد',
      customerPhone: '07901234567',
      customerAddress: 'بغداد، العراق',
      totalAmount: '1100',
      notes: 'يفضل التسليم مساءً'
    },
    {
      productName: 'آيربودز برو',
      quantity: 2,
      customerName: 'فاطمة علي',
      customerPhone: '07801234567',
      customerAddress: 'البصرة، العراق',
      totalAmount: '500',
      notes: 'لون أبيض'
    }
  ];

  const addedOrders = [];

  // إضافة طلبات
  for (const order of orders) {
    const result = await makeRequest('POST', '/api/orders', order, true);
    if (result.success) {
      addedOrders.push(result.data);
      console.log(`✅ تم إضافة طلب: ${order.productName}`);
    } else {
      console.log(`❌ فشل في إضافة الطلب: ${order.productName}`);
    }
  }

  // الحصول على جميع الطلبات
  const getResult = await makeRequest('GET', '/api/orders', null, true);
  if (getResult.success) {
    console.log(`✅ تم الحصول على ${getResult.data.length} طلب`);
  }

  // تحديث حالة طلب
  if (addedOrders.length > 0) {
    const updateResult = await makeRequest('PUT', `/api/orders/${addedOrders[0]._id}/status`, {
      status: 'processing'
    }, true);

    if (updateResult.success) {
      console.log('✅ تم تحديث حالة الطلب إلى "قيد المعالجة"');
    }
  }

  return addedOrders;
}

async function testStoreConfiguration() {
  console.log('\n--- اختبار تكوين المتجر ---');

  const storeInfo = {
    name: 'متجر التكنولوجيا المتقدمة',
    address: 'شارع الحبيبية، بغداد، العراق',
    description: 'متجر متخصص في بيع أحدث المنتجات التكنولوجية والإلكترونية بأسعار منافسة وجودة عالية',
    phone: '+964 ************',
    email: '<EMAIL>',
    website: 'https://techstore.iq'
  };

  const result = await makeRequest('POST', '/api/store/info', storeInfo, true);

  if (result.success) {
    console.log('✅ تم تحديث معلومات المتجر بنجاح');
    console.log(`   الاسم: ${result.data.name}`);
    console.log(`   العنوان: ${result.data.address}`);
    console.log(`   الهاتف: ${result.data.phone}`);
  } else {
    console.log('❌ فشل في تحديث معلومات المتجر:', result.error);
  }
}

async function testGeminiAPI() {
  console.log('\n--- اختبار Gemini API ---');

  // اختبار الاتصال
  const testResult = await makeRequest('GET', '/api/messages/test-gemini');
  if (testResult.success) {
    console.log('✅ اختبار الاتصال بـ Gemini API نجح');
  } else {
    console.log('❌ فشل اختبار الاتصال بـ Gemini API:', testResult.error);
  }

  // اختبار التصحيح
  const debugResult = await makeRequest('GET', '/api/messages/debug-gemini');
  if (debugResult.success) {
    console.log('✅ اختبار تصحيح Gemini API نجح');
  } else {
    console.log('❌ فشل اختبار تصحيح Gemini API:', debugResult.error);
  }

  // اختبار المحادثة
  const convResult = await makeRequest('GET', '/api/messages/debug-conversation');
  if (convResult.success) {
    console.log('✅ اختبار محادثة Gemini API نجح');
  } else {
    console.log('❌ فشل اختبار محادثة Gemini API:', convResult.error);
  }
}

async function runAdvancedTests() {
  try {
    console.log('🚀 بدء الاختبارات المتقدمة للتطبيق...');

    // Connect to database
    await database.connect();
    console.log('✅ تم الاتصال بقاعدة البيانات');

    // Setup test user
    const userSetup = await setupTestUser();
    if (!userSetup) {
      throw new Error('Failed to setup test user');
    }

    // Run advanced tests
    await testComplexProductScenario();
    await testStoreConfiguration();
    await testOrderManagement();
    await testGeminiAPI();
    await testConversationFlow();

    console.log('\n🎉 تم إنجاز جميع الاختبارات المتقدمة بنجاح!');
    console.log('\n📊 ملخص النتائج:');
    console.log('   ✅ إدارة المنتجات المعقدة');
    console.log('   ✅ تكوين المتجر الشامل');
    console.log('   ✅ إدارة الطلبات');
    console.log('   ✅ اختبار Gemini API');
    console.log('   ✅ تدفق المحادثة التفاعلية');

  } catch (error) {
    console.error('\n❌ فشل في تشغيل الاختبارات المتقدمة:', error.message);
  } finally {
    // Disconnect from database
    await database.disconnect();
    console.log('✅ تم قطع الاتصال بقاعدة البيانات');
    process.exit(0);
  }
}

// Run the advanced tests
runAdvancedTests();
