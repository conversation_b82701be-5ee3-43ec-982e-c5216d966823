require('dotenv').config();
const axios = require('axios');
const database = require('../config/database');

const BASE_URL = 'http://localhost:3001';
let authToken = null;
let testUserId = null;

// Test data
const testUser = {
  username: 'testuser_' + Date.now(),
  password: 'testpassword123',
  email: '<EMAIL>',
  name: 'Test User'
};

const testProduct = {
  name: 'منتج تجريبي',
  price: '100 دولار',
  description: 'وصف المنتج التجريبي'
};

const testStoreInfo = {
  name: 'متجر تجريبي',
  address: 'عنوان تجريبي',
  description: 'وصف المتجر التجريبي'
};

async function makeRequest(method, endpoint, data = null, useAuth = false) {
  try {
    const config = {
      method,
      url: `${BASE_URL}${endpoint}`,
      headers: {}
    };

    if (useAuth && authToken) {
      config.headers['x-auth-token'] = authToken;
    }

    if (data) {
      config.data = data;
      config.headers['Content-Type'] = 'application/json';
    }

    const response = await axios(config);
    return { success: true, data: response.data, status: response.status };
  } catch (error) {
    return {
      success: false,
      error: error.response ? error.response.data : error.message,
      status: error.response ? error.response.status : 500
    };
  }
}

async function testUserRegistration() {
  console.log('\n--- اختبار تسجيل المستخدم ---');
  
  const result = await makeRequest('POST', '/api/auth/register', testUser);
  
  if (result.success) {
    console.log('✅ تم تسجيل المستخدم بنجاح');
    authToken = result.data.token;
    testUserId = result.data.user.id;
    console.log(`   المعرف: ${testUserId}`);
    console.log(`   الرمز المميز: ${authToken.substring(0, 20)}...`);
  } else {
    console.log('❌ فشل في تسجيل المستخدم:', result.error);
    throw new Error('User registration failed');
  }
}

async function testUserLogin() {
  console.log('\n--- اختبار تسجيل الدخول ---');
  
  const result = await makeRequest('POST', '/api/auth/login', {
    username: testUser.username,
    password: testUser.password
  });
  
  if (result.success) {
    console.log('✅ تم تسجيل الدخول بنجاح');
    authToken = result.data.token;
    console.log(`   الرمز المميز الجديد: ${authToken.substring(0, 20)}...`);
  } else {
    console.log('❌ فشل في تسجيل الدخول:', result.error);
  }
}

async function testGetCurrentUser() {
  console.log('\n--- اختبار الحصول على بيانات المستخدم الحالي ---');
  
  const result = await makeRequest('GET', '/api/auth/me', null, true);
  
  if (result.success) {
    console.log('✅ تم الحصول على بيانات المستخدم بنجاح');
    console.log(`   اسم المستخدم: ${result.data.username}`);
    console.log(`   البريد الإلكتروني: ${result.data.email}`);
  } else {
    console.log('❌ فشل في الحصول على بيانات المستخدم:', result.error);
  }
}

async function testProductOperations() {
  console.log('\n--- اختبار عمليات المنتجات ---');
  
  // إضافة منتج
  console.log('إضافة منتج جديد...');
  const addResult = await makeRequest('POST', '/api/products', testProduct, true);
  
  if (addResult.success) {
    console.log('✅ تم إضافة المنتج بنجاح');
    const productId = addResult.data._id;
    console.log(`   معرف المنتج: ${productId}`);
    
    // الحصول على المنتجات
    console.log('الحصول على قائمة المنتجات...');
    const getResult = await makeRequest('GET', '/api/products', null, true);
    
    if (getResult.success) {
      console.log(`✅ تم الحصول على ${getResult.data.length} منتج`);
      
      // تحديث المنتج
      console.log('تحديث المنتج...');
      const updateResult = await makeRequest('PUT', `/api/products/${productId}`, {
        name: 'منتج محدث',
        price: '150 دولار'
      }, true);
      
      if (updateResult.success) {
        console.log('✅ تم تحديث المنتج بنجاح');
        
        // حذف المنتج
        console.log('حذف المنتج...');
        const deleteResult = await makeRequest('DELETE', `/api/products/${productId}`, null, true);
        
        if (deleteResult.success) {
          console.log('✅ تم حذف المنتج بنجاح');
        } else {
          console.log('❌ فشل في حذف المنتج:', deleteResult.error);
        }
      } else {
        console.log('❌ فشل في تحديث المنتج:', updateResult.error);
      }
    } else {
      console.log('❌ فشل في الحصول على المنتجات:', getResult.error);
    }
  } else {
    console.log('❌ فشل في إضافة المنتج:', addResult.error);
  }
}

async function testStoreOperations() {
  console.log('\n--- اختبار عمليات المتجر ---');
  
  // الحصول على معلومات المتجر
  console.log('الحصول على معلومات المتجر...');
  const getResult = await makeRequest('GET', '/api/store/info', null, true);
  
  if (getResult.success) {
    console.log('✅ تم الحصول على معلومات المتجر بنجاح');
    
    // تحديث معلومات المتجر
    console.log('تحديث معلومات المتجر...');
    const updateResult = await makeRequest('POST', '/api/store/info', testStoreInfo, true);
    
    if (updateResult.success) {
      console.log('✅ تم تحديث معلومات المتجر بنجاح');
      console.log(`   اسم المتجر: ${updateResult.data.name}`);
    } else {
      console.log('❌ فشل في تحديث معلومات المتجر:', updateResult.error);
    }
  } else {
    console.log('❌ فشل في الحصول على معلومات المتجر:', getResult.error);
  }
}

async function testMessageOperations() {
  console.log('\n--- اختبار عمليات الرسائل ---');
  
  // إرسال رسالة
  console.log('إرسال رسالة تجريبية...');
  const chatResult = await makeRequest('POST', '/api/messages/chat', {
    message: 'مرحبا، هذه رسالة تجريبية',
    platform: 'test'
  }, true);
  
  if (chatResult.success) {
    console.log('✅ تم إرسال الرسالة والحصول على رد بنجاح');
    console.log(`   الرد: ${chatResult.data.message.substring(0, 50)}...`);
    
    // الحصول على تاريخ الرسائل
    console.log('الحصول على تاريخ الرسائل...');
    const messagesResult = await makeRequest('GET', '/api/messages', null, true);
    
    if (messagesResult.success) {
      console.log(`✅ تم الحصول على ${messagesResult.data.length} رسالة`);
      
      // الحصول على إحصائيات الرسائل
      console.log('الحصول على إحصائيات الرسائل...');
      const statsResult = await makeRequest('GET', '/api/messages/stats', null, true);
      
      if (statsResult.success) {
        console.log('✅ تم الحصول على إحصائيات الرسائل بنجاح');
        console.log(`   إجمالي الرسائل: ${statsResult.data.totalMessages}`);
        console.log(`   رسائل المستخدم: ${statsResult.data.userMessages}`);
        console.log(`   رسائل الذكاء الاصطناعي: ${statsResult.data.aiMessages}`);
      } else {
        console.log('❌ فشل في الحصول على إحصائيات الرسائل:', statsResult.error);
      }
    } else {
      console.log('❌ فشل في الحصول على تاريخ الرسائل:', messagesResult.error);
    }
  } else {
    console.log('❌ فشل في إرسال الرسالة:', chatResult.error);
  }
}

async function testOAuthOperations() {
  console.log('\n--- اختبار عمليات OAuth ---');
  
  // الحصول على الحسابات المتصلة
  console.log('الحصول على الحسابات المتصلة...');
  const accountsResult = await makeRequest('GET', '/api/oauth/accounts', null, true);
  
  if (accountsResult.success) {
    console.log('✅ تم الحصول على الحسابات المتصلة بنجاح');
    console.log(`   متصل: ${accountsResult.data.connected}`);
    if (accountsResult.data.accounts) {
      console.log(`   عدد الحسابات: ${accountsResult.data.accounts.length}`);
    }
  } else {
    console.log('❌ فشل في الحصول على الحسابات المتصلة:', accountsResult.error);
  }
}

async function runAllTests() {
  try {
    console.log('🚀 بدء اختبار جميع وظائف التطبيق...');
    
    // Connect to database
    await database.connect();
    console.log('✅ تم الاتصال بقاعدة البيانات');
    
    await testUserRegistration();
    await testUserLogin();
    await testGetCurrentUser();
    await testProductOperations();
    await testStoreOperations();
    await testMessageOperations();
    await testOAuthOperations();
    
    console.log('\n🎉 تم إنجاز جميع الاختبارات بنجاح!');
    
  } catch (error) {
    console.error('\n❌ فشل في تشغيل الاختبارات:', error.message);
  } finally {
    // Disconnect from database
    await database.disconnect();
    console.log('✅ تم قطع الاتصال بقاعدة البيانات');
    process.exit(0);
  }
}

// Run the tests
runAllTests();
