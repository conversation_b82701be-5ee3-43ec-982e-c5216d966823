/**
 * Admin panel script
 */
document.addEventListener('DOMContentLoaded', () => {
  // App state
  const state = {
    isAdmin: false,
    users: [],
    codes: [],
    stats: null,
    currentPage: null
  };

  // DOM Elements
  const elements = {
    // Navigation
    adminNav: document.getElementById('admin-nav'),
    btnAdminLogout: document.getElementById('btn-admin-logout'),

    // Pages
    pages: document.querySelectorAll('.page'),

    // Forms
    adminLoginForm: document.getElementById('admin-login-form'),
    addCodeForm: document.getElementById('add-code-form'),

    // Dashboard
    totalOrders: document.getElementById('total-orders'),
    totalProducts: document.getElementById('total-products'),
    totalMessages: document.getElementById('total-messages'),
    pendingOrders: document.getElementById('pending-orders'),

    // Users
    usersTableBody: document.getElementById('users-table-body'),
    noUsers: document.getElementById('no-users'),

    // Codes
    codesTableBody: document.getElementById('codes-table-body'),
    noCodes: document.getElementById('no-codes'),
    btnGenerateCode: document.getElementById('btn-generate-code'),
    codeTypeRadios: document.getElementsByName('code-type'),
    codeDescription: document.getElementById('code-description'),

    // Modals
    addCodeModal: new bootstrap.Modal(document.getElementById('add-code-modal')),
    errorModal: new bootstrap.Modal(document.getElementById('admin-error-modal')),
    errorMessage: document.getElementById('admin-error-message')
  };

  // Charts
  let ordersPerDayChart = null;
  let topProductsChart = null;

  // Initialize the application
  const init = async () => {
    // Check if admin is logged in
    try {
      const result = await API.admin.checkStatus();
      state.isAdmin = result.isAdmin;
      updateUI();

      if (state.isAdmin) {
        navigateTo('dashboard');
      } else {
        navigateTo('admin-login');
      }
    } catch (error) {
      navigateTo('admin-login');
    }

    // Set up event listeners
    setupEventListeners();
  };

  // Set up event listeners
  const setupEventListeners = () => {
    // Navigation
    document.querySelectorAll('[data-page]').forEach(link => {
      link.addEventListener('click', (e) => {
        e.preventDefault();
        const page = e.currentTarget.getAttribute('data-page');
        navigateTo(page);
      });
    });

    // Admin logout
    elements.btnAdminLogout.addEventListener('click', async () => {
      try {
        await API.admin.logout();
        state.isAdmin = false;
        updateUI();
        navigateTo('admin-login');
      } catch (error) {
        showError(error.message);
      }
    });

    // Admin login form
    elements.adminLoginForm.addEventListener('submit', async (e) => {
      e.preventDefault();
      const username = document.getElementById('admin-username').value;
      const password = document.getElementById('admin-password').value;

      try {
        await API.admin.login({ username, password });
        state.isAdmin = true;
        updateUI();
        navigateTo('dashboard');
      } catch (error) {
        showError(error.message);
      }
    });

    // Generate code button
    elements.btnGenerateCode.addEventListener('click', async () => {
      // Get selected code type
      let type = 'full';
      for (const radio of elements.codeTypeRadios) {
        if (radio.checked) {
          type = radio.value;
          break;
        }
      }

      const description = elements.codeDescription.value.trim();

      try {
        await API.admin.generateCode({ type, description });
        elements.addCodeModal.hide();
        elements.addCodeForm.reset();
        loadCodes();
      } catch (error) {
        showError(error.message);
      }
    });
  };

  // Navigate to a page
  const navigateTo = (page) => {
    // Check if admin is authenticated for protected pages
    const protectedPages = ['dashboard', 'users', 'codes'];
    if (protectedPages.includes(page) && !state.isAdmin) {
      page = 'admin-login';
    }

    // Hide all pages
    elements.pages.forEach(p => {
      p.style.display = 'none';
    });

    // Show selected page
    const selectedPage = document.getElementById(`page-${page}`);
    if (selectedPage) {
      selectedPage.style.display = 'block';
      state.currentPage = page;

      // Load page-specific data
      if (state.isAdmin) {
        switch (page) {
          case 'dashboard':
            loadStats();
            break;
          case 'users':
            loadUsers();
            break;
          case 'codes':
            loadCodes();
            break;
        }
      }
    }
  };

  // Update UI based on authentication state
  const updateUI = () => {
    if (state.isAdmin) {
      elements.adminNav.style.display = 'flex';
      elements.btnAdminLogout.style.display = 'block';
    } else {
      elements.adminNav.style.display = 'none';
      elements.btnAdminLogout.style.display = 'none';
    }
  };

  // Load statistics
  const loadStats = async () => {
    try {
      const stats = await API.admin.getStats();
      state.stats = stats;

      // Update dashboard counters
      elements.totalOrders.textContent = stats.totalOrders || 0;
      elements.totalProducts.textContent = stats.totalProducts || 0;
      elements.totalMessages.textContent = stats.totalMessages || 0;
      elements.pendingOrders.textContent = stats.pendingOrders || 0;

      // Create or update charts
      createOrdersPerDayChart(stats.ordersPerDay || {});
      createTopProductsChart(stats.topProducts || []);
    } catch (error) {
      showError('حدث خطأ أثناء تحميل الإحصائيات');
      console.error('Error loading stats:', error);
    }
  };

  // Create orders per day chart
  const createOrdersPerDayChart = (data) => {
    const ctx = document.getElementById('orders-per-day-chart');
    if (!ctx) return;

    // Destroy existing chart if it exists
    if (ordersPerDayChart) {
      ordersPerDayChart.destroy();
    }

    // Sort dates
    const sortedDates = Object.keys(data).sort();
    const values = sortedDates.map(date => data[date]);

    ordersPerDayChart = new Chart(ctx.getContext('2d'), {
      type: 'line',
      data: {
        labels: sortedDates,
        datasets: [{
          label: 'عدد الطلبات',
          data: values,
          backgroundColor: 'rgba(67, 97, 238, 0.2)',
          borderColor: 'rgba(67, 97, 238, 1)',
          borderWidth: 2,
          tension: 0.3
        }]
      },
      options: {
        responsive: true,
        plugins: {
          legend: {
            position: 'top',
          },
          title: {
            display: true,
            text: 'الطلبات حسب اليوم'
          }
        },
        scales: {
          y: {
            beginAtZero: true
          }
        }
      }
    });
  };

  // Create top products chart
  const createTopProductsChart = (data) => {
    const ctx = document.getElementById('top-products-chart');
    if (!ctx) return;

    // Destroy existing chart if it exists
    if (topProductsChart) {
      topProductsChart.destroy();
    }

    const products = data.map(item => item.name || 'غير محدد');
    const values = data.map(item => item.count);

    topProductsChart = new Chart(ctx.getContext('2d'), {
      type: 'bar',
      data: {
        labels: products,
        datasets: [{
          label: 'عدد الطلبات',
          data: values,
          backgroundColor: 'rgba(67, 97, 238, 0.7)',
          borderColor: 'rgba(67, 97, 238, 1)',
          borderWidth: 1
        }]
      },
      options: {
        responsive: true,
        plugins: {
          legend: {
            position: 'top',
          },
          title: {
            display: true,
            text: 'أكثر المنتجات طلباً'
          }
        },
        scales: {
          y: {
            beginAtZero: true
          }
        }
      }
    });
  };

  // Load users
  const loadUsers = async () => {
    try {
      const users = await API.admin.getUsers();
      state.users = users;

      if (users.length === 0) {
        elements.usersTableBody.innerHTML = '';
        elements.noUsers.style.display = 'block';
        return;
      }

      elements.noUsers.style.display = 'none';

      // Render users table
      elements.usersTableBody.innerHTML = users.map((user, index) => {
        // Format expiry date
        let expiryDate = 'غير مفعل';
        if (user.activationExpiry) {
          const date = new Date(user.activationExpiry);
          expiryDate = date.toLocaleDateString('ar-IQ');
        }

        // Format creation date
        const creationDate = new Date(user.createdAt).toLocaleDateString('ar-IQ');

        return `
          <tr>
            <td>${index + 1}</td>
            <td>${user.name}</td>
            <td>${user.username}</td>
            <td>${user.email}</td>
            <td>${user.freeMessagesRemaining || 0}</td>
            <td>${expiryDate}</td>
            <td>${creationDate}</td>
          </tr>
        `;
      }).join('');
    } catch (error) {
      showError('حدث خطأ أثناء تحميل المستخدمين');
      console.error('Error loading users:', error);
    }
  };

  // Load activation codes
  const loadCodes = async () => {
    try {
      const codes = await API.admin.getCodes();
      state.codes = codes;

      if (codes.length === 0) {
        elements.codesTableBody.innerHTML = '';
        elements.noCodes.style.display = 'block';
        return;
      }

      elements.noCodes.style.display = 'none';

      // Render codes table
      elements.codesTableBody.innerHTML = codes.map(code => {
        // Format dates
        const creationDate = new Date(code.createdAt).toLocaleDateString('ar-IQ');
        let usedDate = '-';
        if (code.usedAt) {
          usedDate = new Date(code.usedAt).toLocaleDateString('ar-IQ');
        }

        // Format type
        const type = code.type === 'full' ? 'كامل (30 يوم)' : 'مؤقت (50 رسالة)';

        // Format status
        const status = code.used ?
          '<span class="badge bg-secondary">مستخدم</span>' :
          '<span class="badge bg-success">متاح</span>';

        return `
          <tr>
            <td><strong>${code.code}</strong></td>
            <td>${type}</td>
            <td>${code.description || '-'}</td>
            <td>${status}</td>
            <td>${creationDate}</td>
            <td>${usedDate}</td>
            <td>${code.usedBy || '-'}</td>
            <td>
              ${!code.used ? `
                <button class="btn btn-sm btn-danger delete-code" data-code="${code.code}">
                  <i class="fas fa-trash"></i>
                </button>
              ` : ''}
            </td>
          </tr>
        `;
      }).join('');

      // Add event listeners to delete buttons
      document.querySelectorAll('.delete-code').forEach(btn => {
        btn.addEventListener('click', async (e) => {
          if (confirm('هل أنت متأكد من حذف هذا الكود؟')) {
            const code = e.currentTarget.getAttribute('data-code');
            try {
              await API.admin.deleteCode(code);
              loadCodes();
            } catch (error) {
              showError(error.message);
            }
          }
        });
      });
    } catch (error) {
      showError('حدث خطأ أثناء تحميل أكواد التفعيل');
      console.error('Error loading codes:', error);
    }
  };

  // Show error message
  const showError = (message) => {
    elements.errorMessage.textContent = message;
    elements.errorModal.show();
  };

  // Initialize the application
  init();
});
